import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

const CourseDetailsPage = () => {
    const { courseId } = useParams();
    const [course, setCourse] = useState(null);
    const [lessons, setLessons] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchCourseDetails = async () => {
            try {
                const [courseResponse, lessonsResponse] = await Promise.all([
                    axios.get(`${API}/courses/${courseId}`),
                    axios.get(`${API}/courses/${courseId}/lessons`)
                ]);
                setCourse(courseResponse.data);
                setLessons(lessonsResponse.data);
            } catch (error) {
                console.error('Error fetching course details:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchCourseDetails();
    }, [courseId]);

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (!course) {
        return <div>Course not found</div>;
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                    <img src={course.cover_image || 'https://via.placeholder.com/800x400'} alt={course.title} className="w-full h-64 object-cover" />
                    <div className="p-6">
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
                        <p className="text-gray-600 mb-4">{course.description}</p>
                        <div className="flex items-center mb-4">
                            <p className="text-gray-700 font-semibold mr-4">Instructor: {course.instructor_name}</p>
                            <p className="text-gray-700">Category: {course.category}</p>
                        </div>
                        <div className="flex items-center">
                            <p className="text-gray-700 mr-4">Price: ${course.price}</p>
                            <p className="text-gray-700">Duration: {course.duration_minutes} minutes</p>
                        </div>
                    </div>
                </div>

                <div className="mt-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Lessons</h2>
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        {lessons.map(lesson => (
                            <div key={lesson.id} className="border-b py-4">
                                <h3 className="text-xl font-semibold text-gray-800">{lesson.title}</h3>
                                <p className="text-gray-600">{lesson.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CourseDetailsPage;
