from fastapi import Fast<PERSON><PERSON>, HTTP<PERSON>x<PERSON>, Depends, Header
from fastapi.middleware.cors import CORSMiddleware
from dev_data import mock_db
import os
import uvicorn

# Import all route modules
from routes.auth import create_auth_router
from routes.courses import create_courses_router
from routes.lessons import create_lessons_router
from routes.dashboard import create_dashboard_router
from routes.admin import create_admin_router
from routes.public import create_public_router

# Create the main FastAPI app
app = FastAPI(
    title="AI Academy LMS API",
    description="Enhanced Learning Management System API with modular architecture",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database initialization
db = mock_db

# Create and include all routers
auth_router = create_auth_router(db)
courses_router = create_courses_router(db)
lessons_router = create_lessons_router(db)
dashboard_router = create_dashboard_router(db)
admin_router = create_admin_router(db)
public_router = create_public_router(db)

# Include all routers
app.include_router(auth_router)
app.include_router(courses_router)
app.include_router(lessons_router)
app.include_router(dashboard_router)
app.include_router(admin_router)
app.include_router(public_router)

# Additional admin endpoints that need special handling
@app.get("/api/newsletter/subscribers")
async def get_newsletter_subscribers(authorization: str = Header(None)):
    """Get newsletter subscribers (admin only)"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    subscribers = db.find("newsletter_subscribers", {})
    print(f"✅ Found {len(subscribers)} newsletter subscribers")
    return {"subscribers": subscribers}

@app.get("/api/admin/branding")
async def get_branding_settings(authorization: str = Header(None)):
    """Get branding settings (admin only)"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    branding = db.find_one("branding_settings", {})
    if not branding:
        branding = {
            "id": "branding-1",
            "logo_url": "/logo.png",
            "site_title": "AI Academy",
            "site_description": "Learn AI and Machine Learning",
            "primary_color": "#3B82F6",
            "secondary_color": "#1E40AF"
        }
    
    print(f"✅ Branding settings retrieved")
    return branding

@app.get("/api/admin/instructor-page")
async def get_instructor_page_content(authorization: str = Header(None)):
    """Get instructor page content (admin only)"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    content = {
        "id": "instructor-page-1",
        "hero_title": "Become an AI Instructor",
        "hero_subtitle": "Share your expertise and help others learn AI",
        "cta_title": "Ready to Start Teaching?",
        "cta_subtitle": "Join our community of expert instructors",
        "cta_button_text": "Apply Now"
    }
    
    print(f"✅ Instructor page content retrieved")
    return content

@app.get("/api/admin/instructor-profiles")
async def get_admin_instructor_profiles(authorization: str = Header(None)):
    """Get instructor profiles (admin only)"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    profiles = db.find("instructor_profiles", {})
    print(f"✅ Found {len(profiles)} instructor profiles")
    return profiles

@app.get("/api/admin/testimonials")
async def get_admin_testimonials(authorization: str = Header(None)):
    """Get testimonials (admin only)"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    testimonials = [
        {
            "id": "testimonial-1",
            "name": "John Smith",
            "title": "Data Scientist",
            "company": "Tech Corp",
            "rating": 5.0,
            "testimonial": "Amazing courses! Really helped me advance my career.",
            "order": 1,
            "is_active": True
        }
    ]
    
    print(f"✅ Found {len(testimonials)} testimonials")
    return testimonials

@app.post("/api/admin/testimonials/init-default")
async def init_default_testimonials(authorization: str = Header(None)):
    """Initialize default testimonials"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    print(f"✅ Default testimonials initialized")
    return {"message": "Default testimonials created successfully"}

@app.post("/api/admin/instructor-profiles/init-default")
async def init_default_instructor_profiles(authorization: str = Header(None)):
    """Initialize default instructor profiles"""
    from utils.auth import get_current_user_with_db
    current_user = get_current_user_with_db(db, authorization)
    
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    print(f"✅ Default instructor profiles initialized")
    return {"message": "Default instructor profiles created successfully"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "mode": "development", "database": "mock", "version": "2.0.0"}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "AI Academy LMS API v2.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    print("🚀 Starting Enhanced LMS API Server v2.0...")
    print("📊 Using Mock Database")
    print("🔗 CORS enabled for http://localhost:3000")
    print("🎯 Server will start on http://localhost:8000")
    print("📚 API documentation available at http://localhost:8000/docs")
    print()
    print("🔐 Test Credentials:")
    print("   Admin: <EMAIL> / Admin123!")
    print("   Instructor: <EMAIL> / Admin123!")
    print("   Student: <EMAIL> / Admin123!")
    print()
    print("✨ New Features Added:")
    print("   - User Registration")
    print("   - Course Details & Lessons")
    print("   - Enrollment System")
    print("   - Progress Tracking")
    print("   - Enhanced Admin Panel")
    print("   - Modular Architecture")
    print()
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
