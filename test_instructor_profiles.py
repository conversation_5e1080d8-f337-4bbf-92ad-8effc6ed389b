import requests
import json
import os
from dotenv import load_dotenv
import sys

# Load environment variables
load_dotenv('/app/frontend/.env')
BACKEND_URL = os.environ.get('REACT_APP_BACKEND_URL')
if not BACKEND_URL:
    print("Error: REACT_APP_BACKEND_URL not found in environment variables")
    sys.exit(1)

API_URL = f"{BACKEND_URL}/api"
print(f"Using API URL: {API_URL}")

def create_super_admin():
    """Create a super admin for testing"""
    admin_credentials = {
        "email": "<EMAIL>",
        "password": "adminpass123",
        "name": "Test Admin"
    }
    
    # Try to create super admin
    response = requests.post(f"{API_URL}/auth/create-super-admin", json=admin_credentials)
    if response.status_code == 400 and "Super admin already exists" in response.json().get("detail", ""):
        print("Super admin already exists, logging in...")
    elif response.status_code == 200:
        print("Super admin created successfully")
    else:
        print(f"Unexpected response: {response.status_code} - {response.text}")
    
    # Login with admin credentials
    login_data = {
        "email": admin_credentials["email"],
        "password": admin_credentials["password"]
    }
    response = requests.post(f"{API_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("Admin login successful")
        return token
    else:
        print(f"Admin login failed: {response.status_code} - {response.text}")
        return None

def test_instructor_profiles_api(token):
    """Test 1: Instructor Profiles API"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Get all instructor profiles (admin endpoint)
    print("\n--- Test 1: Instructor Profiles API ---")
    response = requests.get(f"{API_URL}/admin/instructor-profiles", headers=headers)
    
    if response.status_code == 200:
        profiles = response.json()
        print(f"Found {len(profiles)} instructor profiles")
        
        # Check if we need to initialize default profiles
        if len(profiles) == 0:
            print("No profiles found, initializing defaults...")
            init_response = requests.post(f"{API_URL}/admin/instructor-profiles/init-default", headers=headers)
            
            if init_response.status_code == 200:
                print("Default profiles initialized successfully")
                
                # Get profiles again
                response = requests.get(f"{API_URL}/admin/instructor-profiles", headers=headers)
                if response.status_code == 200:
                    profiles = response.json()
                    print(f"Now found {len(profiles)} instructor profiles")
                else:
                    print(f"Failed to get profiles after initialization: {response.status_code} - {response.text}")
                    return False
            else:
                print(f"Failed to initialize default profiles: {init_response.status_code} - {init_response.text}")
                return False
        
        # Verify expertise is an array in all profiles
        for profile in profiles:
            if not isinstance(profile["expertise"], list):
                print(f"ERROR: Profile {profile['id']} has expertise not stored as an array")
                return False
            print(f"Profile {profile['name']} has expertise: {profile['expertise']}")
        
        print("✅ All admin profiles have expertise correctly stored as arrays")
        return True
    else:
        print(f"Failed to get instructor profiles: {response.status_code} - {response.text}")
        return False

def test_instructor_profile_creation(token):
    """Test 2: Instructor Profile Creation with Expertise"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n--- Test 2: Instructor Profile Creation with Expertise ---")
    
    # Create a new instructor profile with expertise as an array
    test_profile = {
        "name": "Test Instructor",
        "title": "AI Testing Expert",
        "avatar": "https://example.com/avatar.jpg",
        "bio": "Expert in testing AI applications",
        "expertise": ["Testing", "AI", "Python"],
        "experience": "5+ years",
        "students": "1,000+",
        "rating": 4.8,
        "courses": 3,
        "company": "Test Company",
        "education": "PhD in Computer Science"
    }
    
    response = requests.post(f"{API_URL}/admin/instructor-profiles", json=test_profile, headers=headers)
    
    if response.status_code == 200:
        created_profile = response.json()
        profile_id = created_profile["id"]
        print(f"Created instructor profile with ID: {profile_id}")
        
        # Verify expertise is an array
        if isinstance(created_profile["expertise"], list):
            print(f"✅ Expertise is correctly stored as an array: {created_profile['expertise']}")
            
            # Test updating the profile with new expertise
            update_data = {
                "expertise": ["Python", "TensorFlow", "PyTorch", "Computer Vision"]
            }
            
            update_response = requests.put(f"{API_URL}/admin/instructor-profiles/{profile_id}", json=update_data, headers=headers)
            
            if update_response.status_code == 200:
                updated_profile = update_response.json()
                
                # Verify expertise is still an array after update
                if isinstance(updated_profile["expertise"], list):
                    print(f"✅ Updated expertise is correctly stored as an array: {updated_profile['expertise']}")
                    return True
                else:
                    print("❌ Updated expertise is not stored as an array")
                    return False
            else:
                print(f"Failed to update instructor profile: {update_response.status_code} - {update_response.text}")
                return False
        else:
            print("❌ Expertise is not stored as an array")
            return False
    else:
        print(f"Failed to create instructor profile: {response.status_code} - {response.text}")
        return False

def test_public_instructor_profiles():
    """Test 3: Public Instructor Profiles API"""
    print("\n--- Test 3: Public Instructor Profiles API ---")
    
    # Get public instructor profiles
    response = requests.get(f"{API_URL}/instructor-profiles")
    
    if response.status_code == 200:
        profiles = response.json()
        print(f"Found {len(profiles)} public instructor profiles")
        
        # Verify expertise is an array in all public profiles
        for profile in profiles:
            if not isinstance(profile["expertise"], list):
                print(f"ERROR: Public profile {profile['id']} has expertise not stored as an array")
                return False
            print(f"Public profile {profile['name']} has expertise: {profile['expertise']}")
        
        print("✅ All public profiles have expertise correctly stored as arrays")
        return True
    else:
        print(f"Failed to get public instructor profiles: {response.status_code} - {response.text}")
        return False

def main():
    # Create super admin and get token
    token = create_super_admin()
    if not token:
        print("Failed to create or login as super admin")
        return False
    
    # Run tests
    test1_result = test_instructor_profiles_api(token)
    test2_result = test_instructor_profile_creation(token)
    test3_result = test_public_instructor_profiles()
    
    # Print summary
    print("\n--- TEST SUMMARY ---")
    print(f"Test 1 (Instructor Profiles API): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    print(f"Test 2 (Instructor Profile Creation): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    print(f"Test 3 (Public Instructor Profiles): {'✅ PASSED' if test3_result else '❌ FAILED'}")
    
    overall_result = test1_result and test2_result and test3_result
    print(f"\nOVERALL RESULT: {'✅ PASSED' if overall_result else '❌ FAILED'}")
    
    return overall_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)