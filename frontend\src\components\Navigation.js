import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { fetchBranding } from '../api';

const Navigation = () => {
  const { user, logout } = useAuth();
  const currentPath = window.location.pathname;
  const [branding, setBranding] = useState({
    logo_url: 'https://static.wixstatic.com/media/e226d5_1613e089c216411ca2e365caaf93709f~mv2.jpg/v1/fill/w_200,h_170,al_c,q_80,usm_0.66_1.00_0.01,enc_avif,quality_auto/Logo%20500x500%20px_edited.jpg',
    site_title: 'AI Academy'
  });

  useEffect(() => {
    const getBranding = async () => {
      try {
        const response = await fetchBranding();
        setBranding(response.data);
      } catch (error) {
        console.log('Using default branding');
      }
    };
    getBranding();
  }, []);

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <a href="/" className="flex-shrink-0 flex items-center">
              <img 
                src={branding.logo_url} 
                alt={branding.site_title}
                className="w-10 h-10 mr-3 object-contain rounded-lg"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div 
                className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mr-3 items-center justify-center text-white font-bold text-sm hidden" 
                style={{display: 'none'}}
              >
                AI
              </div>
              <span className="text-xl font-bold text-gray-900">{branding.site_title}</span>
            </a>
            
            {/* Navigation Links */}
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="/"
                className={`${
                  currentPath === '/'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Home
              </a>
              {user && (
                <a
                  href="/dashboard"
                  className={`${
                    currentPath === '/dashboard'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  Dashboard
                </a>
              )}
              {user?.role === 'admin' && (
                <a
                  href="/admin"
                  className={`${
                    currentPath === '/admin'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  CMS Admin
                </a>
              )}
              <a
                href="/instructors"
                className={`${
                  currentPath === '/instructors'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Instructors
              </a>
              <a
                href="/contact"
                className={`${
                  currentPath === '/contact'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Contact
              </a>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {user ? (
              <>
                <span className="text-gray-700">Welcome, {user.name}</span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {user.role}
                </span>
                <button
                  onClick={logout}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition duration-200"
                >
                  Logout
                </button>
              </>
            ) : (
              <div className="space-x-2">
                <a href="/login" className="text-gray-700 hover:text-gray-900">Login</a>
                <a href="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Sign Up</a>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
