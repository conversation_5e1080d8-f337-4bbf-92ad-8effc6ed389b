#!/usr/bin/env python3
"""
Simple LMS Server with Mock Data
No MongoDB required - uses in-memory mock data for development
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import bcrypt
import jwt
from datetime import datetime, timedelta
import uuid

# Import mock data
from dev_data import mock_db

# Create FastAPI app
app = FastAPI(
    title="Simple LMS API",
    description="Learning Management System API with Mock Data",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"

# Pydantic models
class UserLogin(BaseModel):
    email: str
    password: str

class UserCreate(BaseModel):
    email: str
    password: str
    name: str
    role: str = "student"

class User(BaseModel):
    id: str
    email: str
    name: str
    role: str
    created_at: str
    bio: Optional[str] = None
    avatar_url: Optional[str] = None

# Helper functions
def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=401, detail="Invalid token")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user = mock_db.find_one("users", {"email": email})
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    
    return User(**user)

# Routes
@app.get("/")
async def root():
    return {"message": "Simple LMS API is running", "status": "ok"}

@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

# Auth routes
@app.post("/api/auth/login")
async def login(login_data: UserLogin):
    user = mock_db.find_one("users", {"email": login_data.email})
    if not user or not verify_password(login_data.password, user['password']):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    access_token = create_access_token(data={"sub": user['email']})
    user_obj = User(**user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_obj
    }

@app.post("/api/auth/register")
async def register(user_data: UserCreate):
    # Check if user exists
    existing_user = mock_db.find_one("users", {"email": user_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    hashed_password = hash_password(user_data.password)
    user_dict = {
        "id": str(uuid.uuid4()),
        "email": user_data.email,
        "name": user_data.name,
        "role": user_data.role,
        "password": hashed_password,
        "created_at": datetime.utcnow().isoformat(),
        "bio": None,
        "avatar_url": None
    }
    
    mock_db.insert_one("users", user_dict)
    
    # Create access token
    access_token = create_access_token(data={"sub": user_data.email})
    user_obj = User(**user_dict)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_obj
    }

@app.get("/api/auth/me")
async def get_me(current_user: User = Depends(get_current_user)):
    return current_user

# Public routes
@app.get("/api/courses")
async def get_courses():
    courses = mock_db.find("courses", {"is_published": True})
    return {"data": courses}

@app.get("/api/branding")
async def get_branding():
    branding = mock_db.find_one("branding_settings", {})
    if not branding:
        # Return default branding
        branding = {
            "site_title": "AI Academy",
            "site_description": "Learn AI and Machine Learning",
            "logo_url": None,
            "primary_color": "#3B82F6",
            "secondary_color": "#1E40AF"
        }
    return {"data": branding}

@app.get("/api/instructor-profiles")
async def get_instructor_profiles():
    profiles = mock_db.find("instructor_profiles", {"is_active": True})
    return {"data": profiles}

@app.get("/api/testimonials")
async def get_testimonials():
    testimonials = mock_db.find("testimonials", {"is_active": True})
    return {"data": testimonials}

# Course routes
@app.get("/api/courses/{course_id}")
async def get_course(course_id: str):
    course = mock_db.find_one("courses", {"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    return {"data": course}

@app.get("/api/courses/{course_id}/lessons")
async def get_course_lessons(course_id: str):
    lessons = mock_db.find("lessons", {"course_id": course_id})
    return {"data": lessons}

# Dashboard routes
@app.get("/api/dashboard/stats")
async def get_dashboard_stats(current_user: User = Depends(get_current_user)):
    if current_user.role == "student":
        enrollments = mock_db.find("enrollments", {"user_id": current_user.id})
        return {
            "data": {
                "enrolled_courses": len(enrollments),
                "completed_courses": 0,
                "total_progress": 0
            }
        }
    elif current_user.role == "instructor":
        courses = mock_db.find("courses", {"instructor_id": current_user.id})
        return {
            "data": {
                "total_courses": len(courses),
                "published_courses": len([c for c in courses if c.get("is_published", False)]),
                "total_students": 0
            }
        }
    else:
        return {"data": {"message": "Admin dashboard stats"}}

@app.get("/api/dashboard/courses")
async def get_dashboard_courses(current_user: User = Depends(get_current_user)):
    if current_user.role == "student":
        enrollments = mock_db.find("enrollments", {"user_id": current_user.id})
        course_ids = [e["course_id"] for e in enrollments]
        courses = [mock_db.find_one("courses", {"id": cid}) for cid in course_ids]
        courses = [c for c in courses if c]  # Filter out None values
        return {"data": courses}
    elif current_user.role == "instructor":
        courses = mock_db.find("courses", {"instructor_id": current_user.id})
        return {"data": courses}
    else:
        courses = mock_db.find("courses", {})
        return {"data": courses}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple LMS Server...")
    print("📊 Using mock data (no MongoDB required)")
    print("🌐 Server will be available at: http://localhost:9000")
    print("📖 API docs at: http://localhost:9000/docs")
    print("\n🔐 Test Credentials:")
    print("   Student: <EMAIL> / Admin123!")
    print("   Instructor: <EMAIL> / Admin123!")
    print("   Admin: <EMAIL> / Admin123!")
    uvicorn.run(app, host="0.0.0.0", port=9000)
