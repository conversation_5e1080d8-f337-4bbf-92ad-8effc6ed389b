import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { fetchCourses, fetchTestimonials, fetchInstructorProfiles, enrollInCourse } from '../api';
import NewsletterSignup from './NewsletterSignup';

const HomePage = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState([]);
  const [testimonials, setTestimonials] = useState([]);
  const [instructorProfiles, setInstructorProfiles] = useState([]);
  const [loading, setLoading] = useState(true);

  // Professional AI course images
  const courseImages = [
    'https://images.unsplash.com/photo-1516110833967-0b5716ca1387',
    'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
    'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg',
    'https://images.unsplash.com/photo-1498050108023-c5249f4df085',
    'https://images.unsplash.com/photo-1591696331111-ef9586a5b17a'
  ];

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      const [coursesResponse, testimonialsResponse, instructorsResponse] = await Promise.all([
        fetchCourses(),
        fetchTestimonials(),
        fetchInstructorProfiles().catch(() => ({ data: [] }))
      ]);
      setCourses(coursesResponse.data);
      setTestimonials(testimonialsResponse.data);
      setInstructorProfiles(instructorsResponse.data || []);
    } catch (error) {
      console.error('Error fetching home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async (courseId) => {
    try {
      await enrollInCourse(courseId);
      alert('Enrolled successfully! Check your dashboard to start learning.');
      window.location.href = '/dashboard';
    } catch (error) {
      alert(error.response?.data?.detail || 'Enrollment failed');
    }
  };

  const getCourseImage = (index) => {
    return courseImages[index % courseImages.length];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Master AI with Expert-Led Courses
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Learn cutting-edge AI technologies from industry experts. Build real projects. Advance your career.
            </p>
            {!user && (
              <div className="space-x-4">
                <a href="/register" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200">
                  Start Learning
                </a>
                <a href="/login" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-200">
                  Sign In
                </a>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose AI Academy?</h2>
            <p className="text-gray-600 text-lg">Everything you need to become an AI expert</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Expert Instructors</h3>
              <p className="text-gray-600">Learn from industry professionals with real-world AI experience</p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Hands-on Projects</h3>
              <p className="text-gray-600">Build real AI applications and add them to your portfolio</p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Fast Track Learning</h3>
              <p className="text-gray-600">Accelerated curriculum designed for quick skill acquisition</p>
            </div>
          </div>
        </div>
      </div>

      {/* Expert Instructors Section */}
      {instructorProfiles.length > 0 && (
        <div className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Expert Instructors</h2>
              <p className="text-xl text-gray-600">
                Learn from industry professionals with real-world AI experience
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {instructorProfiles.slice(0, 6).map((instructor) => (
                <div key={instructor.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="p-6">
                    {/* Profile Image */}
                    <div className="flex justify-center mb-4">
                      <img 
                        src={instructor.avatar || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'} 
                        alt={instructor.name}
                        className="w-20 h-20 rounded-full object-cover border-4 border-blue-100"
                      />
                    </div>
                    
                    {/* Name and Title */}
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-gray-900">{instructor.name}</h3>
                      <p className="text-blue-600 font-semibold">{instructor.title}</p>
                      {instructor.company && (
                        <p className="text-gray-600 text-sm">{instructor.company}</p>
                      )}
                    </div>
                    
                    {/* Bio */}
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{instructor.bio}</p>
                    
                    {/* Expertise Tags */}
                    {instructor.expertise && instructor.expertise.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Expertise:</h4>
                        <div className="flex flex-wrap gap-1">
                          {instructor.expertise.slice(0, 3).map((skill, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                              {skill}
                            </span>
                          ))}
                          {instructor.expertise.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                              +{instructor.expertise.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Stats */}
                    <div className="flex justify-between text-sm text-gray-600 mb-4">
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">{instructor.students || '0'}</div>
                        <div>Students</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">{instructor.courses || '0'}</div>
                        <div>Courses</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">⭐ {instructor.rating || '4.9'}</div>
                        <div>Rating</div>
                      </div>
                    </div>
                    
                    {/* View Courses Button */}
                    <button 
                      onClick={() => {
                        // Scroll to courses section
                        const coursesSection = document.getElementById('courses-section');
                        if (coursesSection) {
                          coursesSection.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                    >
                      View Courses
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            {/* View All Instructors Link */}
            <div className="text-center mt-8">
              <a 
                href="/instructors"
                className="inline-flex items-center px-6 py-3 bg-white border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition duration-200"
              >
                Meet All Our Instructors
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Newsletter Signup Section */}
      <div className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Stay Updated on AI Trends</h2>
            <p className="text-xl text-indigo-100 mb-8">
              Get exclusive AI insights, free resources, and course updates delivered to your inbox
            </p>
            
            <NewsletterSignup />
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Students Say</h2>
            <p className="text-gray-600 text-lg">Join thousands of professionals advancing their AI careers</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.length === 0 ? (
              // Fallback testimonials if none are configured
              [
                {
                  name: "Sarah Rodriguez",
                  title: "Data Scientist at Google",
                  testimonial: "This course completely transformed my understanding of machine learning. The hands-on projects were incredibly valuable for my career transition.",
                  rating: 5
                },
                {
                  name: "Michael Chen", 
                  title: "AI Engineer at Tesla",
                  testimonial: "The instructor's expertise and clear explanations made complex AI concepts easy to understand. Highly recommended for anyone serious about AI.",
                  rating: 5
                },
                {
                  name: "Emily Parker",
                  title: "Product Manager at Microsoft", 
                  testimonial: "Perfect blend of theory and practice. I was able to implement AI solutions at work within weeks of completing the course.",
                  rating: 5
                }
              ].map((testimonial, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                      <span className="text-blue-600 font-bold text-lg">{testimonial.name.split(' ').map(n => n[0]).join('')}</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-sm">{testimonial.title}</p>
                    </div>
                  </div>
                  <div className="flex mb-4">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span key={star} className="text-yellow-400 text-lg">★</span>
                    ))}
                  </div>
                  <p className="text-gray-700 italic">"{testimonial.testimonial}"</p>
                </div>
              ))
            ) : (
              testimonials.slice(0, 3).map((testimonial) => (
                <div key={testimonial.id} className="bg-white p-8 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    {testimonial.avatar ? (
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span className="text-blue-600 font-bold text-lg">{testimonial.name.charAt(0)}</span>
                      </div>
                    )}
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-sm">{testimonial.title} at {testimonial.company}</p>
                    </div>
                  </div>
                  <div className="flex mb-4">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span 
                        key={star} 
                        className={`text-lg ${star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      >
                        ★
                      </span>
                    ))}
                  </div>
                  <p className="text-gray-700 italic">"{testimonial.testimonial}"</p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Courses Section */}
      <div id="courses-section" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Courses</h2>
            <p className="text-gray-600 text-lg">Start your AI journey with these courses</p>
          </div>
          
          {courses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No courses available yet. Check back soon!</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course, index) => (
                <div key={course.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition duration-300 course-card">
                  <div className="relative">
                    <img 
                      src={course.cover_image || getCourseImage(index)} 
                      alt={course.title} 
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        {course.price === 0 ? 'FREE' : `$${course.price}`}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="mb-2">
                      <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">
                        {course.category}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold mb-3 text-gray-900">{course.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{course.description}</p>
                    
                    <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        {course.instructor_name}
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                        </svg>
                        {course.student_count} students
                      </div>
                    </div>
                    
                    {course.duration_minutes && (
                      <div className="flex items-center mb-4 text-sm text-gray-500">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {Math.floor(course.duration_minutes / 60)}h {course.duration_minutes % 60}m
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      {user ? (
                        <button
                          onClick={() => handleEnroll(course.id)}
                          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                        >
                          Enroll Now
                        </button>
                      ) : (
                        <a 
                          href="/register" 
                          className="flex-1 text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                        >
                          Sign up to enroll
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomePage;
