import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import axios from 'axios';

const API = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const CoursePage = () => {
  const { user } = useAuth();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [progress, setProgress] = useState(null);
  const [discussions, setDiscussions] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showLessonForm, setShowLessonForm] = useState(false);
  const [showDiscussionForm, setShowDiscussionForm] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [activeTab, setActiveTab] = useState('lessons');
  const [lessonFormData, setLessonFormData] = useState({
    title: '',
    description: '',
    type: 'text',
    content: '',
    duration_minutes: ''
  });
  const [discussionText, setDiscussionText] = useState('');
  const [reviewData, setReviewData] = useState({
    rating: 5,
    review: ''
  });

  // Get course ID from URL
  const courseId = window.location.pathname.split('/').pop();

  useEffect(() => {
    fetchCourseData();
  }, [courseId]);

  const fetchCourseData = async () => {
    try {
      const promises = [
        axios.get(`${API}/courses/${courseId}`),
        axios.get(`${API}/courses/${courseId}/lessons`),
        axios.get(`${API}/courses/${courseId}/reviews`)
      ];

      // Add discussions if user is enrolled or instructor
      if (user) {
        promises.push(axios.get(`${API}/courses/${courseId}/discussions`).catch(() => ({ data: [] })));
      }
      
      const responses = await Promise.all(promises);
      const [courseResponse, lessonsResponse, reviewsResponse, discussionsResponse] = responses;
      
      setCourse(courseResponse.data);
      setLessons(lessonsResponse.data);
      setReviews(reviewsResponse.data);
      if (discussionsResponse) {
        setDiscussions(discussionsResponse.data);
      }

      // If student, fetch progress
      if (user?.role === 'student') {
        try {
          const progressResponse = await axios.get(`${API}/courses/${courseId}/progress`);
          setProgress(progressResponse.data);
        } catch (error) {
          console.log('Not enrolled in course');
        }
      }
    } catch (error) {
      console.error('Error fetching course data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    try {
      await axios.post(`${API}/courses/${courseId}/enroll`);
      alert('Enrolled successfully!');
      fetchCourseData();
    } catch (error) {
      alert(error.response?.data?.detail || 'Enrollment failed');
    }
  };

  const handlePublishCourse = async () => {
    try {
      await axios.put(`${API}/courses/${courseId}/publish`);
      alert('Course published successfully!');
      fetchCourseData();
    } catch (error) {
      alert('Failed to publish course');
    }
  };

  const handleCreateLesson = async (e) => {
    e.preventDefault();
    try {
      const lessonData = {
        ...lessonFormData,
        order: lessons.length + 1,
        duration_minutes: parseInt(lessonFormData.duration_minutes) || 0
      };
      
      await axios.post(`${API}/courses/${courseId}/lessons`, lessonData);
      alert('Lesson created successfully!');
      setShowLessonForm(false);
      setLessonFormData({
        title: '',
        description: '',
        type: 'text',
        content: '',
        duration_minutes: ''
      });
      fetchCourseData();
    } catch (error) {
      alert('Failed to create lesson');
    }
  };

  const handleCompleteLesson = async (lessonId) => {
    try {
      await axios.post(`${API}/lessons/${lessonId}/complete`);
      alert('Lesson completed!');
      fetchCourseData();
    } catch (error) {
      alert('Failed to mark lesson as complete');
    }
  };

  const handleCreateDiscussion = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/courses/${courseId}/discussions`, { message: discussionText });
      setDiscussionText('');
      setShowDiscussionForm(false);
      fetchCourseData();
    } catch (error) {
      alert('Failed to post discussion');
    }
  };

  const handleCreateReview = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/courses/${courseId}/reviews`, reviewData);
      setReviewData({ rating: 5, review: '' });
      setShowReviewForm(false);
      alert('Review submitted successfully!');
      fetchCourseData();
    } catch (error) {
      alert(error.response?.data?.detail || 'Failed to submit review');
    }
  };

  const renderStars = (rating, interactive = false, onChange = null) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={!interactive}
            onClick={() => interactive && onChange && onChange(star)}
            className={`text-xl ${star <= rating ? 'text-yellow-400' : 'text-gray-300'} ${
              interactive ? 'hover:text-yellow-500 cursor-pointer' : 'cursor-default'
            }`}
          >
            ★
          </button>
        ))}
      </div>
    );
  };

  const getAverageRating = () => {
    if (reviews.length === 0) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Course not found</h1>
          <a href="/dashboard" className="text-blue-600 hover:text-blue-800">Return to Dashboard</a>
        </div>
      </div>
    );
  }

  const isInstructor = user?.role === 'instructor' && course.instructor_id === user.id;
  const isStudent = user?.role === 'student';
  const isEnrolled = progress !== null;

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Course Header */}
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-8">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{course.title}</h1>
                  <p className="text-gray-600 mb-4">{course.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                    <span>By {course.instructor_name}</span>
                    <span>•</span>
                    <span>{course.student_count} students</span>
                    <span>•</span>
                    <span>{course.category}</span>
                    {course.duration_minutes && (
                      <>
                        <span>•</span>
                        <span>{course.duration_minutes} minutes</span>
                      </>
                    )}
                  </div>
                  
                  {/* Course Rating */}
                  {reviews.length > 0 && (
                    <div className="flex items-center space-x-2 mb-4">
                      {renderStars(Math.round(getAverageRating()))}
                      <span className="text-lg font-semibold text-gray-900">{getAverageRating()}</span>
                      <span className="text-gray-500">({reviews.length} reviews)</span>
                    </div>
                  )}
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600 mb-2">
                    {course.price === 0 ? 'Free' : `${course.price}`}
                  </div>
                  <div className={`text-sm px-3 py-1 rounded-full ${
                    course.is_published 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {course.is_published ? 'Published' : 'Draft'}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                {isInstructor && (
                  <>
                    {!course.is_published && (
                      <button
                        onClick={handlePublishCourse}
                        className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                      >
                        Publish Course
                      </button>
                    )}
                    <button
                      onClick={() => setShowLessonForm(true)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                      Add Lesson
                    </button>
                  </>
                )}
                
                {isStudent && !isEnrolled && course.is_published && (
                  <button
                    onClick={handleEnroll}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Enroll Now
                  </button>
                )}
                
                {isEnrolled && (
                  <div className="bg-green-100 text-green-800 px-4 py-2 rounded-md">
                    Enrolled - {Math.round(progress?.completion_percentage || 0)}% Complete
                  </div>
                )}
              </div>
            </div>
            
            {/* Navigation Tabs */}
            <div className="border-t border-gray-200">
              <nav className="flex space-x-8 px-6">
                {['lessons', 'discussions', 'reviews'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                      activeTab === tab
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab}
                    {tab === 'lessons' && ` (${lessons.length})`}
                    {tab === 'discussions' && ` (${discussions.length})`}
                    {tab === 'reviews' && ` (${reviews.length})`}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Lesson Creation Form */}
          {showLessonForm && isInstructor && (
            <div className="bg-white rounded-lg shadow mb-8 p-6">
              <h2 className="text-xl font-bold mb-4">Create New Lesson</h2>
              <form onSubmit={handleCreateLesson} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Lesson Title</label>
                  <input
                    type="text"
                    required
                    value={lessonFormData.title}
                    onChange={(e) => setLessonFormData({...lessonFormData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    value={lessonFormData.description}
                    onChange={(e) => setLessonFormData({...lessonFormData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                    <select
                      value={lessonFormData.type}
                      onChange={(e) => setLessonFormData({...lessonFormData, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="text">Text Content</option>
                      <option value="video">Video</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                    <input
                      type="number"
                      value={lessonFormData.duration_minutes}
                      onChange={(e) => setLessonFormData({...lessonFormData, duration_minutes: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {lessonFormData.type === 'video' ? 'Video URL (YouTube)' : 'Content'}
                  </label>
                  {lessonFormData.type === 'video' ? (
                    <input
                      type="url"
                      required
                      value={lessonFormData.content}
                      onChange={(e) => setLessonFormData({...lessonFormData, content: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://www.youtube.com/watch?v=..."
                    />
                  ) : (
                    <textarea
                      required
                      value={lessonFormData.content}
                      onChange={(e) => setLessonFormData({...lessonFormData, content: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={6}
                      placeholder="Enter lesson content..."
                    />
                  )}
                </div>
                
                <div className="flex space-x-4">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Create Lesson
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowLessonForm(false)}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Content Sections */}
          <div className="bg-white rounded-lg shadow">
            {activeTab === 'lessons' && (
              <div className="p-6">
                {lessons.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No lessons yet.</p>
                    {isInstructor && (
                      <button
                        onClick={() => setShowLessonForm(true)}
                        className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Add First Lesson
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {lessons.map((lesson, index) => {
                      const lessonProgress = progress?.lessons?.find(l => l.lesson.id === lesson.id);
                      const isCompleted = lessonProgress?.completed || false;
                      
                      return (
                        <div key={lesson.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3">
                                <span className="text-sm text-gray-500">Lesson {index + 1}</span>
                                {isCompleted && (
                                  <span className="text-green-600">✓</span>
                                )}
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">{lesson.title}</h3>
                              {lesson.description && (
                                <p className="text-gray-600 mt-1">{lesson.description}</p>
                              )}
                              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-2">
                                <span className="capitalize">{lesson.type}</span>
                                {lesson.duration_minutes && (
                                  <>
                                    <span>•</span>
                                    <span>{lesson.duration_minutes} minutes</span>
                                  </>
                                )}
                              </div>
                            </div>
                            
                            {isEnrolled && !isCompleted && (
                              <button
                                onClick={() => handleCompleteLesson(lesson.id)}
                                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                              >
                                Mark Complete
                              </button>
                            )}
                          </div>
                          
                          {/* Lesson Content Preview */}
                          {lesson.type === 'video' && lesson.content && (
                            <div className="mt-4">
                              <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                                <span className="text-gray-500">Video: {lesson.content}</span>
                              </div>
                            </div>
                          )}
                          
                          {lesson.type === 'text' && lesson.content && (
                            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                              <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: lesson.content.substring(0, 300) + '...' }} />
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {activeTab === 'discussions' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Course Discussions</h3>
                {(isEnrolled || isInstructor) && (
                  <button
                    onClick={() => setShowDiscussionForm(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Start Discussion
                  </button>
                )}
              }

              {showDiscussionForm && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <form onSubmit={handleCreateDiscussion}>
                    <textarea
                      value={discussionText}
                      onChange={(e) => setDiscussionText(e.target.value)}
                      placeholder="Share your thoughts, ask questions, or help fellow students..."
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={4}
                      required
                    />
                    <div className="flex space-x-3 mt-3">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Post Discussion
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowDiscussionForm(false)}
                        className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {discussions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No discussions yet. Start the conversation!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {discussions.map((discussion) => (
                    <div key={discussion.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">
                            {discussion.user_name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-semibold text-gray-900">{discussion.user_name}</span>
                            {discussion.is_instructor && (
                              <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                                Instructor
                              </span>
                            )}
                            <span className="text-sm text-gray-500">
                              {new Date(discussion.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-gray-700">{discussion.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Course Reviews</h3>
                {isEnrolled && (
                  <button
                    onClick={() => setShowReviewForm(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Write Review
                  </button>
                )}
              }

              {showReviewForm && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <form onSubmit={handleCreateReview}>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                      {renderStars(reviewData.rating, true, (rating) => 
                        setReviewData({ ...reviewData, rating })
                      )}
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Review</label>
                      <textarea
                        value={reviewData.review}
                        onChange={(e) => setReviewData({ ...reviewData, review: e.target.value })}
                        placeholder="Share your experience with this course..."
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        rows={4}
                        required
                      />
                    </div>
                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Submit Review
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowReviewForm(false)}
                        className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {reviews.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No reviews yet. Be the first to review this course!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-semibold">
                            {review.user_name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="font-semibold text-gray-900">{review.user_name}</span>
                            <span className="text-sm text-gray-500">
                              {new Date(review.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 mb-2">
                            {renderStars(review.rating)}
                            <span className="text-sm text-gray-600">({review.rating}/5)</span>
                          </div>
                          <p className="text-gray-700">{review.review}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CoursePage;
