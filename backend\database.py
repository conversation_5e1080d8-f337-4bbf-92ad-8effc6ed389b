"""
Database wrapper that handles both MongoDB and mock data
"""

import os
from typing import Dict, Any, List, Optional
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ServerSelectionTimeoutError
from dev_data import mock_db
import asyncio

class DatabaseWrapper:
    """Database wrapper that can use either MongoDB or mock data"""
    
    def __init__(self):
        self.client = None
        self.db = None
        self.use_mock = False
        self.dev_mode = os.getenv("DEV_MODE", "false").lower() == "true"
        
    async def connect(self):
        """Connect to database or fall back to mock data"""
        if self.dev_mode:
            print("🔧 DEV_MODE enabled - using mock data")
            self.use_mock = True
            return
        
        mongo_url = os.getenv("MONGO_URL")
        db_name = os.getenv("DB_NAME", "lmsdb")
        
        if not mongo_url:
            print("❌ No MONGO_URL found - using mock data")
            self.use_mock = True
            return
        
        try:
            print(f"🔄 Attempting to connect to MongoDB: {mongo_url[:50]}...")
            self.client = AsyncIOMotorClient(mongo_url, serverSelectionTimeoutMS=5000)
            
            # Test connection
            await self.client.server_info()
            self.db = self.client[db_name]
            
            print("✅ Connected to MongoDB successfully!")
            self.use_mock = False
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {str(e)[:100]}...")
            print("🔄 Falling back to mock data for development")
            self.use_mock = True
            
            if self.client:
                self.client.close()
                self.client = None
    
    def get_collection(self, name: str):
        """Get collection wrapper"""
        return CollectionWrapper(name, self.db, self.use_mock)

class CollectionWrapper:
    """Collection wrapper that handles both MongoDB collections and mock data"""
    
    def __init__(self, name: str, db, use_mock: bool):
        self.name = name
        self.db = db
        self.use_mock = use_mock
        self.collection = db[name] if db else None
    
    async def find_one(self, query: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Find one document"""
        if self.use_mock:
            return mock_db.find_one(self.name, query or {})
        
        if self.collection:
            return await self.collection.find_one(query or {})
        
        return None
    
    def find(self, query: Dict[str, Any] = None):
        """Find documents - returns cursor-like object"""
        return CursorWrapper(self.name, query or {}, self.collection, self.use_mock)
    
    async def insert_one(self, document: Dict[str, Any]) -> Any:
        """Insert one document"""
        if self.use_mock:
            doc_id = mock_db.insert_one(self.name, document)
            return MockInsertResult(doc_id)
        
        if self.collection:
            return await self.collection.insert_one(document)
        
        return MockInsertResult("mock-id")
    
    async def update_one(self, query: Dict[str, Any], update: Dict[str, Any]) -> Any:
        """Update one document"""
        if self.use_mock:
            success = mock_db.update_one(self.name, query, update)
            return MockUpdateResult(success)
        
        if self.collection:
            return await self.collection.update_one(query, update)
        
        return MockUpdateResult(False)
    
    async def delete_one(self, query: Dict[str, Any]) -> Any:
        """Delete one document"""
        if self.use_mock:
            success = mock_db.delete_one(self.name, query)
            return MockDeleteResult(success)
        
        if self.collection:
            return await self.collection.delete_one(query)
        
        return MockDeleteResult(False)
    
    async def count_documents(self, query: Dict[str, Any] = None) -> int:
        """Count documents"""
        if self.use_mock:
            return mock_db.count_documents(self.name, query or {})
        
        if self.collection:
            return await self.collection.count_documents(query or {})
        
        return 0

class CursorWrapper:
    """Cursor wrapper for find operations"""
    
    def __init__(self, collection_name: str, query: Dict[str, Any], collection, use_mock: bool):
        self.collection_name = collection_name
        self.query = query
        self.collection = collection
        self.use_mock = use_mock
        self._sort_field = None
        self._sort_direction = 1
        self._limit_count = None
    
    def sort(self, field: str, direction: int = 1):
        """Sort results"""
        self._sort_field = field
        self._sort_direction = direction
        return self
    
    def limit(self, count: int):
        """Limit results"""
        self._limit_count = count
        return self
    
    async def to_list(self, length: int = None) -> List[Dict[str, Any]]:
        """Convert cursor to list"""
        if self.use_mock:
            results = mock_db.find(self.collection_name, self.query)
            
            # Apply sorting
            if self._sort_field:
                reverse = self._sort_direction == -1
                results.sort(key=lambda x: x.get(self._sort_field, 0), reverse=reverse)
            
            # Apply limit
            if self._limit_count:
                results = results[:self._limit_count]
            elif length:
                results = results[:length]
            
            return results
        
        if self.collection:
            cursor = self.collection.find(self.query)
            
            if self._sort_field:
                cursor = cursor.sort(self._sort_field, self._sort_direction)
            
            if self._limit_count:
                cursor = cursor.limit(self._limit_count)
            
            return await cursor.to_list(length)
        
        return []

class MockInsertResult:
    """Mock insert result"""
    def __init__(self, inserted_id):
        self.inserted_id = inserted_id

class MockUpdateResult:
    """Mock update result"""
    def __init__(self, success: bool):
        self.modified_count = 1 if success else 0

class MockDeleteResult:
    """Mock delete result"""
    def __init__(self, success: bool):
        self.deleted_count = 1 if success else 0

# Global database instance
database = DatabaseWrapper()
