import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import axios from 'axios';
import './App.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

// Auth Context
const AuthContext = React.createContext();

const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await axios.get(`${API}/auth/me`);
      setUser(response.data);
    } catch (error) {
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    const response = await axios.post(`${API}/auth/login`, { email, password });
    const { access_token, user: userData } = response.data;
    
    localStorage.setItem('token', access_token);
    axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
    setUser(userData);
    
    return userData;
  };

  const register = async (email, password, name, role = 'student') => {
    const response = await axios.post(`${API}/auth/register`, { email, password, name, role });
    const { access_token, user: userData } = response.data;
    
    localStorage.setItem('token', access_token);
    axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
    setUser(userData);
    
    return userData;
  };

  const logout = () => {
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Newsletter Signup Component
const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    
    try {
      await axios.post(`${API}/newsletter/subscribe`, { email, name });
      setMessage('🎉 Successfully subscribed! Check your email for AI insights.');
      setEmail('');
      setName('');
    } catch (error) {
      setMessage(error.response?.data?.message || 'Subscription failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <form onSubmit={handleSubmit} className="space-y-4">
        {message && (
          <div className={`p-3 rounded-lg text-center ${
            message.includes('🎉') 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {message}
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3">
          <input
            type="text"
            placeholder="Your name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="flex-1 px-4 py-3 rounded-lg border border-white bg-white bg-opacity-20 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-white focus:bg-opacity-30"
          />
          <input
            type="email"
            placeholder="Your email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="flex-1 px-4 py-3 rounded-lg border border-white bg-white bg-opacity-20 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-white focus:bg-opacity-30"
          />
        </div>
        
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200 disabled:opacity-50"
        >
          {loading ? 'Subscribing...' : '✨ Get AI Insights'}
        </button>
      </form>
    </div>
  );
};

// Components
const Navigation = () => {
  const { user, logout } = useAuth();
  const currentPath = window.location.pathname;
  const [branding, setBranding] = useState({
    logo_url: 'https://static.wixstatic.com/media/e226d5_1613e089c216411ca2e365caaf93709f~mv2.jpg/v1/fill/w_200,h_170,al_c,q_80,usm_0.66_1.00_0.01,enc_avif,quality_auto/Logo%20500x500%20px_edited.jpg',
    site_title: 'AI Academy'
  });

  useEffect(() => {
    // Fetch current branding
    const fetchBranding = async () => {
      try {
        const response = await axios.get(`${API}/branding`);
        setBranding(response.data);
      } catch (error) {
        console.log('Using default branding');
      }
    };
    fetchBranding();
  }, []);

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <a href="/" className="flex-shrink-0 flex items-center">
              <img 
                src={branding.logo_url} 
                alt={branding.site_title}
                className="w-10 h-10 mr-3 object-contain rounded-lg"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div 
                className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mr-3 items-center justify-center text-white font-bold text-sm hidden" 
                style={{display: 'none'}}
              >
                AI
              </div>
              <span className="text-xl font-bold text-gray-900">{branding.site_title}</span>
            </a>
            
            {/* Navigation Links */}
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="/"
                className={`${
                  currentPath === '/' 
                    ? 'border-blue-500 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Home
              </a>
              {user && (
                <a
                  href="/dashboard"
                  className={`${
                    currentPath === '/dashboard' 
                      ? 'border-blue-500 text-blue-600' 
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  Dashboard
                </a>
              )}
              {user?.role === 'admin' && (
                <a
                  href="/admin"
                  className={`${
                    currentPath === '/admin' 
                      ? 'border-blue-500 text-blue-600' 
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  CMS Admin
                </a>
              )}
              <a
                href="/instructors"
                className={`${
                  currentPath === '/instructors' 
                    ? 'border-blue-500 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Instructors
              </a>
              <a
                href="/contact"
                className={`${
                  currentPath === '/contact' 
                    ? 'border-blue-500 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Contact
              </a>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {user ? (
              <>
                <span className="text-gray-700">Welcome, {user.name}</span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {user.role}
                </span>
                <button
                  onClick={logout}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition duration-200"
                >
                  Logout
                </button>
              </>
            ) : (
              <div className="space-x-2">
                <a href="/login" className="text-gray-700 hover:text-gray-900">Login</a>
                <a href="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Sign Up</a>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

const HomePage = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState([]);
  const [testimonials, setTestimonials] = useState([]);
  const [instructorProfiles, setInstructorProfiles] = useState([]);
  const [loading, setLoading] = useState(true);

  // Professional AI course images
  const courseImages = [
    'https://images.unsplash.com/photo-*************-0b5716ca1387',
    'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
    'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg',
    'https://images.unsplash.com/photo-*************-c5249f4df085',
    'https://images.unsplash.com/photo-*************-ef9586a5b17a'
  ];

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      const [coursesResponse, testimonialsResponse, instructorsResponse] = await Promise.all([
        axios.get(`${API}/courses`),
        axios.get(`${API}/testimonials`),
        axios.get(`${API}/instructor-profiles`).catch(() => ({ data: [] }))
      ]);
      setCourses(coursesResponse.data);
      setTestimonials(testimonialsResponse.data);
      setInstructorProfiles(instructorsResponse.data || []);
    } catch (error) {
      console.error('Error fetching home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const enrollInCourse = async (courseId) => {
    try {
      await axios.post(`${API}/courses/${courseId}/enroll`);
      alert('Enrolled successfully! Check your dashboard to start learning.');
      window.location.href = '/dashboard';
    } catch (error) {
      alert(error.response?.data?.detail || 'Enrollment failed');
    }
  };

  const getCourseImage = (index) => {
    return courseImages[index % courseImages.length];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Master AI with Expert-Led Courses
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Learn cutting-edge AI technologies from industry experts. Build real projects. Advance your career.
            </p>
            {!user && (
              <div className="space-x-4">
                <a href="/register" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200">
                  Start Learning
                </a>
                <a href="/login" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-200">
                  Sign In
                </a>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose AI Academy?</h2>
            <p className="text-gray-600 text-lg">Everything you need to become an AI expert</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Expert Instructors</h3>
              <p className="text-gray-600">Learn from industry professionals with real-world AI experience</p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Hands-on Projects</h3>
              <p className="text-gray-600">Build real AI applications and add them to your portfolio</p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Fast Track Learning</h3>
              <p className="text-gray-600">Accelerated curriculum designed for quick skill acquisition</p>
            </div>
          </div>
        </div>
      </div>

      {/* Expert Instructors Section */}
      {instructorProfiles.length > 0 && (
        <div className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Expert Instructors</h2>
              <p className="text-xl text-gray-600">
                Learn from industry professionals with real-world AI experience
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {instructorProfiles.slice(0, 6).map((instructor) => (
                <div key={instructor.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="p-6">
                    {/* Profile Image */}
                    <div className="flex justify-center mb-4">
                      <img 
                        src={instructor.avatar || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'} 
                        alt={instructor.name}
                        className="w-20 h-20 rounded-full object-cover border-4 border-blue-100"
                      />
                    </div>
                    
                    {/* Name and Title */}
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-gray-900">{instructor.name}</h3>
                      <p className="text-blue-600 font-semibold">{instructor.title}</p>
                      {instructor.company && (
                        <p className="text-gray-600 text-sm">{instructor.company}</p>
                      )}
                    </div>
                    
                    {/* Bio */}
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{instructor.bio}</p>
                    
                    {/* Expertise Tags */}
                    {instructor.expertise && instructor.expertise.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Expertise:</h4>
                        <div className="flex flex-wrap gap-1">
                          {instructor.expertise.slice(0, 3).map((skill, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                              {skill}
                            </span>
                          ))}
                          {instructor.expertise.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                              +{instructor.expertise.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Stats */}
                    <div className="flex justify-between text-sm text-gray-600 mb-4">
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">{instructor.students || '0'}</div>
                        <div>Students</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">{instructor.courses || '0'}</div>
                        <div>Courses</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-900">⭐ {instructor.rating || '4.9'}</div>
                        <div>Rating</div>
                      </div>
                    </div>
                    
                    {/* View Courses Button */}
                    <button 
                      onClick={() => {
                        // Scroll to courses section
                        const coursesSection = document.getElementById('courses-section');
                        if (coursesSection) {
                          coursesSection.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                    >
                      View Courses
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            {/* View All Instructors Link */}
            <div className="text-center mt-8">
              <a 
                href="/instructors"
                className="inline-flex items-center px-6 py-3 bg-white border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition duration-200"
              >
                Meet All Our Instructors
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Newsletter Signup Section */}
      <div className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Stay Updated on AI Trends</h2>
            <p className="text-xl text-indigo-100 mb-8">
              Get exclusive AI insights, free resources, and course updates delivered to your inbox
            </p>
            
            <NewsletterSignup />
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Students Say</h2>
            <p className="text-gray-600 text-lg">Join thousands of professionals advancing their AI careers</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.length === 0 ? (
              // Fallback testimonials if none are configured
              [
                {
                  name: "Sarah Rodriguez",
                  title: "Data Scientist at Google",
                  testimonial: "This course completely transformed my understanding of machine learning. The hands-on projects were incredibly valuable for my career transition.",
                  rating: 5
                },
                {
                  name: "Michael Chen", 
                  title: "AI Engineer at Tesla",
                  testimonial: "The instructor's expertise and clear explanations made complex AI concepts easy to understand. Highly recommended for anyone serious about AI.",
                  rating: 5
                },
                {
                  name: "Emily Parker",
                  title: "Product Manager at Microsoft", 
                  testimonial: "Perfect blend of theory and practice. I was able to implement AI solutions at work within weeks of completing the course.",
                  rating: 5
                }
              ].map((testimonial, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                      <span className="text-blue-600 font-bold text-lg">{testimonial.name.split(' ').map(n => n[0]).join('')}</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-sm">{testimonial.title}</p>
                    </div>
                  </div>
                  <div className="flex mb-4">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span key={star} className="text-yellow-400 text-lg">★</span>
                    ))}
                  </div>
                  <p className="text-gray-700 italic">"{testimonial.testimonial}"</p>
                </div>
              ))
            ) : (
              testimonials.slice(0, 3).map((testimonial) => (
                <div key={testimonial.id} className="bg-white p-8 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    {testimonial.avatar ? (
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span className="text-blue-600 font-bold text-lg">{testimonial.name.charAt(0)}</span>
                      </div>
                    )}
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-sm">{testimonial.title} at {testimonial.company}</p>
                    </div>
                  </div>
                  <div className="flex mb-4">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span 
                        key={star} 
                        className={`text-lg ${star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      >
                        ★
                      </span>
                    ))}
                  </div>
                  <p className="text-gray-700 italic">"{testimonial.testimonial}"</p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Courses Section */}
      <div id="courses-section" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Courses</h2>
            <p className="text-gray-600 text-lg">Start your AI journey with these courses</p>
          </div>
          
          {courses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No courses available yet. Check back soon!</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course, index) => (
                <div key={course.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition duration-300 course-card">
                  <div className="relative">
                    <img 
                      src={course.cover_image || getCourseImage(index)} 
                      alt={course.title} 
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        {course.price === 0 ? 'FREE' : `$${course.price}`}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="mb-2">
                      <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">
                        {course.category}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold mb-3 text-gray-900">{course.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{course.description}</p>
                    
                    <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        {course.instructor_name}
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                        </svg>
                        {course.student_count} students
                      </div>
                    </div>
                    
                    {course.duration_minutes && (
                      <div className="flex items-center mb-4 text-sm text-gray-500">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {Math.floor(course.duration_minutes / 60)}h {course.duration_minutes % 60}m
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      {user ? (
                        <button
                          onClick={() => enrollInCourse(course.id)}
                          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                        >
                          Enroll Now
                        </button>
                      ) : (
                        <a 
                          href="/register" 
                          className="flex-1 text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                        >
                          Sign up to enroll
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const LoginPage = () => {
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      await login(email, password);
      window.location.href = '/dashboard';
    } catch (error) {
      setError(error.response?.data?.detail || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mx-auto mb-4"></div>
          <h2 className="text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your password"
              />
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </button>
            </div>

            <div className="text-center">
              <span className="text-sm text-gray-600">
                Don't have an account?{' '}
                <a href="/register" className="font-medium text-blue-600 hover:text-blue-500">
                  Sign up
                </a>
              </span>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const RegisterPage = () => {
  const { register } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'student'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      await register(formData.email, formData.password, formData.name, formData.role);
      window.location.href = '/dashboard';
    } catch (error) {
      setError(error.response?.data?.detail || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mx-auto mb-4"></div>
          <h2 className="text-3xl font-extrabold text-gray-900">Create your account</h2>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}
            
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your password"
              />
            </div>

            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                I want to
              </label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="student">Learn (Student)</option>
                <option value="instructor">Teach (Instructor)</option>
              </select>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Creating account...' : 'Create account'}
              </button>
            </div>

            <div className="text-center">
              <span className="text-sm text-gray-600">
                Already have an account?{' '}
                <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
                  Sign in
                </a>
              </span>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  // Professional AI course images
  const courseImages = [
    'https://images.unsplash.com/photo-*************-0b5716ca1387',
    'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
    'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg',
    'https://images.unsplash.com/photo-*************-c5249f4df085',
    'https://images.unsplash.com/photo-*************-ef9586a5b17a'
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, coursesResponse] = await Promise.all([
        axios.get(`${API}/dashboard/stats`),
        axios.get(`${API}/my-courses`)
      ]);
      
      setStats(statsResponse.data);
      setCourses(coursesResponse.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCourseImage = (index) => {
    return courseImages[index % courseImages.length];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const isNewUser = user?.role === 'instructor' && courses.length === 0;
  const isNewStudent = user?.role === 'student' && courses.length === 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}! 👋
          </h1>
          <p className="text-gray-600 mt-2">
            {user?.role === 'instructor' 
              ? 'Ready to inspire and teach? Manage your courses and track student progress' 
              : 'Continue your AI learning journey and unlock new possibilities'
            }
          </p>
        </div>

        {/* New User Welcome Section */}
        {(isNewUser || isNewStudent) && (
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">
                  {isNewUser ? '🚀 Ready to create your first course?' : '🎯 Ready to start learning?'}
                </h2>
                <p className="text-blue-100 mb-4">
                  {isNewUser 
                    ? 'Share your AI expertise with students around the world. Create engaging courses with videos, text content, and quizzes.'
                    : 'Explore our AI courses and start building your skills today. Learn from industry experts and advance your career.'
                  }
                </p>
                <a
                  href={isNewUser ? '/create-course' : '/'}
                  className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200"
                >
                  {isNewUser ? 'Create Your First Course' : 'Browse Courses'}
                </a>
              </div>
              <div className="hidden md:block">
                <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-4xl">
                    {isNewUser ? '📚' : '🎓'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {user?.role === 'instructor' ? (
              <>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-100 mr-4">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Total Courses</h3>
                      <p className="text-3xl font-bold text-blue-600 mt-1">{stats.total_courses}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-green-100 mr-4">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Total Students</h3>
                      <p className="text-3xl font-bold text-green-600 mt-1">{stats.total_students}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-purple-100 mr-4">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Published Courses</h3>
                      <p className="text-3xl font-bold text-purple-600 mt-1">
                        {courses.filter(c => c.is_published).length}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-100 mr-4">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Enrolled Courses</h3>
                      <p className="text-3xl font-bold text-blue-600 mt-1">{stats.total_enrolled}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-green-100 mr-4">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Completed Courses</h3>
                      <p className="text-3xl font-bold text-green-600 mt-1">{stats.completed_courses}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-yellow-100 mr-4">
                      <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">In Progress</h3>
                      <p className="text-3xl font-bold text-yellow-600 mt-1">
                        {stats.total_enrolled - stats.completed_courses}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Courses Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              {user?.role === 'instructor' ? 'My Courses' : 'My Learning'}
            </h2>
            {user?.role === 'instructor' && (
              <a
                href="/create-course"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
              >
                ✨ Create Course
              </a>
            )}
          </div>
          
          <div className="p-6">
            {courses.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">
                    {user?.role === 'instructor' ? '📚' : '🎓'}
                  </span>
                </div>
                <p className="text-gray-500 text-lg mb-4">
                  {user?.role === 'instructor' 
                    ? 'You haven\'t created any courses yet.' 
                    : 'You haven\'t enrolled in any courses yet.'
                  }
                </p>
                <p className="text-gray-400 mb-6">
                  {user?.role === 'instructor' 
                    ? 'Start sharing your knowledge and expertise with students worldwide.'
                    : 'Discover amazing AI courses and start your learning journey today.'
                  }
                </p>
                {user?.role === 'instructor' ? (
                  <a
                    href="/create-course"
                    className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Your First Course
                  </a>
                ) : (
                  <a
                    href="/"
                    className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Browse Courses
                  </a>
                )}
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {courses.map((course, index) => (
                  <div key={course.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition duration-300">
                    <div className="relative">
                      <img 
                        src={course.cover_image || getCourseImage(index)} 
                        alt={course.title} 
                        className="w-full h-32 object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-semibold ${
                          course.is_published 
                            ? 'bg-green-500 text-white' 
                            : 'bg-yellow-500 text-white'
                        }`}>
                          {course.is_published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <h3 className="text-lg font-bold mb-2 text-gray-900">{course.title}</h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{course.description}</p>
                      
                      <div className="flex items-center justify-between mb-3 text-sm text-gray-500">
                        <span>{course.student_count} students</span>
                        {course.duration_minutes && (
                          <span>{Math.floor(course.duration_minutes / 60)}h {course.duration_minutes % 60}m</span>
                        )}
                      </div>
                      
                      <a
                        href={`/course/${course.id}`}
                        className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                      >
                        {user?.role === 'instructor' ? 'Manage Course' : 'Continue Learning'}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

// Contact Page Component
const ContactPage = () => {
  const [contactInfo, setContactInfo] = useState({});
  const [socialLinks, setSocialLinks] = useState({});
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    fetchContactData();
  }, []);

  const fetchContactData = async () => {
    try {
      const [contactResponse, socialResponse] = await Promise.all([
        axios.get(`${API}/contact-info`),
        axios.get(`${API}/social-links`)
      ]);
      setContactInfo(contactResponse.data);
      setSocialLinks(socialResponse.data);
    } catch (error) {
      console.error('Error fetching contact data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      await axios.post(`${API}/contact/submit`, formData);
      setSubmitted(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      alert('Failed to send message. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Get in Touch</h1>
            <p className="text-xl md:text-2xl text-blue-100">
              Have questions about our AI courses? We're here to help!
            </p>
          </div>
        </div>
      </div>

      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {submitted && (
            <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-green-900 mb-2">Message Sent Successfully!</h3>
              <p className="text-green-700">Thank you for contacting us. We'll get back to you within 24 hours.</p>
            </div>
          )}

          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="+****************"
                      />
                    </div>
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                        Subject *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        required
                        value={formData.subject}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="What's this about?"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Tell us how we can help you..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={submitting}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105 disabled:opacity-50"
                  >
                    {submitting ? 'Sending...' : 'Send Message'}
                  </button>
                </form>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              {/* Address Block */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Contact Information</h3>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <h4 className="font-semibold text-gray-900">Address</h4>
                      <p className="text-gray-600 text-sm">
                        {contactInfo.address}<br />
                        {contactInfo.city}, {contactInfo.state} {contactInfo.zip_code}<br />
                        {contactInfo.country}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <div>
                      <h4 className="font-semibold text-gray-900">Phone</h4>
                      <p className="text-gray-600 text-sm">{contactInfo.phone}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <h4 className="font-semibold text-gray-900">Email</h4>
                      <p className="text-gray-600 text-sm">{contactInfo.email}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h4 className="font-semibold text-gray-900">Business Hours</h4>
                      <p className="text-gray-600 text-sm">{contactInfo.business_hours}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Links */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Follow Us</h3>
                
                <div className="flex space-x-4">
                  {socialLinks.facebook && (
                    <a
                      href={socialLinks.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition duration-200"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                  
                  {socialLinks.youtube && (
                    <a
                      href={socialLinks.youtube}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition duration-200"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                      </svg>
                    </a>
                  )}
                  
                  {socialLinks.linkedin && (
                    <a
                      href={socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-blue-700 rounded-full flex items-center justify-center text-white hover:bg-blue-800 transition duration-200"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  )}
                  
                  {socialLinks.twitter && (
                    <a
                      href={socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition duration-200"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                      </svg>
                    </a>
                  )}
                  
                  {socialLinks.instagram && (
                    <a
                      href={socialLinks.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition duration-200"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// CMS Admin Page Component
const CMSAdminPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('analytics');
  const [analytics, setAnalytics] = useState(null);
  const [content, setContent] = useState([]);
  const [users, setUsers] = useState([]);
  const [webinars, setWebinars] = useState([]);
  const [subscribers, setSubscribers] = useState([]);
  const [branding, setBranding] = useState({ logo_url: '', site_title: '' });
  const [instructorPageContent, setInstructorPageContent] = useState({});
  const [instructorProfiles, setInstructorProfiles] = useState([]);
  const [testimonials, setTestimonials] = useState([]);
  const [contactInfo, setContactInfo] = useState({});
  const [socialLinks, setSocialLinks] = useState({});
  const [loading, setLoading] = useState(true);
  const [editingContent, setEditingContent] = useState({});
  const [editingBranding, setEditingBranding] = useState({ logo_url: '', site_title: '' });
  const [editingInstructorPage, setEditingInstructorPage] = useState({});
  const [editingProfile, setEditingProfile] = useState(null);
  const [editingTestimonial, setEditingTestimonial] = useState(null);
  const [editingContactInfo, setEditingContactInfo] = useState({});
  const [editingSocialLinks, setEditingSocialLinks] = useState({});
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [showTestimonialForm, setShowTestimonialForm] = useState(false);
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [userFormData, setUserFormData] = useState({
    name: '',
    email: '',
    role: 'student',
    password: ''
  });
  const [profileFormData, setProfileFormData] = useState({
    name: '',
    title: '',
    avatar: '',
    bio: '',
    expertise: [],
    experience: '',
    students: '',
    rating: 4.9,
    courses: 0,
    company: '',
    education: '',
    order: 0
  });
  const [testimonialFormData, setTestimonialFormData] = useState({
    name: '',
    title: '',
    company: '',
    avatar: '',
    rating: 5.0,
    testimonial: '',
    order: 0
  });
  const [webinarForm, setWebinarForm] = useState({
    title: '',
    description: '',
    date_time: '',
    duration_minutes: 60,
    max_attendees: 100,
    registration_link: ''
  });

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchAdminData();
    }
  }, [user]);

  const fetchAdminData = async () => {
    try {
      const [analyticsRes, contentRes, usersRes, webinarsRes, subscribersRes, brandingRes, instructorPageRes, profilesRes, testimonialsRes, contactInfoRes, socialLinksRes] = await Promise.all([
        axios.get(`${API}/admin/analytics`),
        axios.get(`${API}/admin/content`),
        axios.get(`${API}/admin/users`),
        axios.get(`${API}/admin/webinars`),
        axios.get(`${API}/newsletter/subscribers`),
        axios.get(`${API}/admin/branding`),
        axios.get(`${API}/admin/instructor-page`),
        axios.get(`${API}/admin/instructor-profiles`).catch(() => ({ data: [] })),
        axios.get(`${API}/admin/testimonials`).catch(() => ({ data: [] })),
        axios.get(`${API}/contact-info`).catch(() => ({ data: {} })),
        axios.get(`${API}/social-links`).catch(() => ({ data: {} }))
      ]);

      setAnalytics(analyticsRes.data);
      setContent(contentRes.data.content || []);
      setUsers(usersRes.data.users || []);
      setWebinars(webinarsRes.data || []);
      setSubscribers(subscribersRes.data.subscribers || []);
      setBranding(brandingRes.data);
      setEditingBranding(brandingRes.data);
      setInstructorPageContent(instructorPageRes.data);
      setEditingInstructorPage(instructorPageRes.data);
      setInstructorProfiles(profilesRes.data || []);
      setTestimonials(testimonialsRes.data || []);
      setContactInfo(contactInfoRes.data);
      setSocialLinks(socialLinksRes.data);
      setEditingContactInfo(contactInfoRes.data);
      setEditingSocialLinks(socialLinksRes.data);
    } catch (error) {
      console.error('Error fetching admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (contentKey, newValue) => {
    try {
      await axios.put(`${API}/admin/content/${contentKey}`, { content_value: newValue });
      alert('Content updated successfully!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to update content');
    }
  };

  const createWebinar = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/admin/webinars`, webinarForm);
      alert('Webinar created successfully!');
      setWebinarForm({
        title: '',
        description: '',
        date_time: '',
        duration_minutes: 60,
        max_attendees: 100,
        registration_link: ''
      });
      fetchAdminData();
    } catch (error) {
      alert('Failed to create webinar');
    }
  };

  const updateBranding = async () => {
    try {
      await axios.put(`${API}/admin/branding`, editingBranding);
      alert('Branding updated successfully!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to update branding');
    }
  };

  const updateInstructorPage = async () => {
    try {
      await axios.put(`${API}/admin/instructor-page`, editingInstructorPage);
      alert('Instructor page updated successfully!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to update instructor page');
    }
  };

  const initDefaultInstructors = async () => {
    try {
      await axios.post(`${API}/admin/instructor-profiles/init-default`);
      alert('Default instructor profiles created!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to create default instructors');
    }
  };

  const createOrUpdateProfile = async () => {
    try {
      if (editingProfile) {
        await axios.put(`${API}/admin/instructor-profiles/${editingProfile.id}`, profileFormData);
        alert('Instructor profile updated successfully!');
      } else {
        await axios.post(`${API}/admin/instructor-profiles`, profileFormData);
        alert('Instructor profile created successfully!');
      }
      
      setShowProfileForm(false);
      setEditingProfile(null);
      setProfileFormData({
        name: '',
        title: '',
        avatar: '',
        bio: '',
        expertise: [],
        experience: '',
        students: '',
        rating: 4.9,
        courses: 0,
        company: '',
        education: '',
        order: 0
      });
      fetchAdminData();
    } catch (error) {
      alert('Failed to save instructor profile');
    }
  };

  const editProfile = (profile) => {
    setEditingProfile(profile);
    setProfileFormData({
      ...profile,
      expertise: Array.isArray(profile.expertise) ? profile.expertise : []
    });
    setShowProfileForm(true);
  };

  const deleteProfile = async (profileId) => {
    if (window.confirm('Are you sure you want to delete this instructor profile?')) {
      try {
        await axios.delete(`${API}/admin/instructor-profiles/${profileId}`);
        alert('Instructor profile deleted successfully!');
        fetchAdminData();
      } catch (error) {
        alert('Failed to delete instructor profile');
      }
    }
  };

  const initDefaultTestimonials = async () => {
    try {
      await axios.post(`${API}/admin/testimonials/init-default`);
      alert('Default testimonials created!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to create default testimonials');
    }
  };

  const createOrUpdateTestimonial = async () => {
    try {
      if (editingTestimonial) {
        await axios.put(`${API}/admin/testimonials/${editingTestimonial.id}`, testimonialFormData);
        alert('Testimonial updated successfully!');
      } else {
        await axios.post(`${API}/admin/testimonials`, testimonialFormData);
        alert('Testimonial created successfully!');
      }
      
      setShowTestimonialForm(false);
      setEditingTestimonial(null);
      setTestimonialFormData({
        name: '',
        title: '',
        company: '',
        avatar: '',
        rating: 5.0,
        testimonial: '',
        order: 0
      });
      fetchAdminData();
    } catch (error) {
      alert('Failed to save testimonial');
    }
  };

  const editTestimonial = (testimonial) => {
    setEditingTestimonial(testimonial);
    setTestimonialFormData(testimonial);
    setShowTestimonialForm(true);
  };

  const deleteTestimonial = async (testimonialId) => {
    if (window.confirm('Are you sure you want to delete this testimonial?')) {
      try {
        await axios.delete(`${API}/admin/testimonials/${testimonialId}`);
        alert('Testimonial deleted successfully!');
        fetchAdminData();
      } catch (error) {
        alert('Failed to delete testimonial');
      }
    }
  };

  // User Management Functions
  const createUser = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/admin/users`, userFormData);
      alert('User created successfully!');
      setUserFormData({ name: '', email: '', role: 'student', password: '' });
      setShowUserForm(false);
      fetchAdminData();
    } catch (error) {
      alert('Failed to create user: ' + (error.response?.data?.detail || 'Unknown error'));
    }
  };

  const updateUser = async (e) => {
    e.preventDefault();
    try {
      const updateData = { ...userFormData };
      if (!updateData.password) {
        delete updateData.password; // Don't update password if not provided
      }
      await axios.put(`${API}/admin/users/${editingUser.id}`, updateData);
      alert('User updated successfully!');
      setEditingUser(null);
      setUserFormData({ name: '', email: '', role: 'student', password: '' });
      fetchAdminData();
    } catch (error) {
      alert('Failed to update user: ' + (error.response?.data?.detail || 'Unknown error'));
    }
  };

  const deleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`${API}/admin/users/${userId}`);
        alert('User deleted successfully!');
        fetchAdminData();
      } catch (error) {
        alert('Failed to delete user: ' + (error.response?.data?.detail || 'Unknown error'));
      }
    }
  };

  // Contact Info Management Functions
  const updateContactInfo = async () => {
    try {
      await axios.put(`${API}/admin/contact-info`, editingContactInfo);
      alert('Contact information updated successfully!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to update contact information');
    }
  };

  // Social Links Management Functions
  const updateSocialLinks = async () => {
    try {
      await axios.put(`${API}/admin/social-links`, editingSocialLinks);
      alert('Social links updated successfully!');
      fetchAdminData();
    } catch (error) {
      alert('Failed to update social links');
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Only super admins can access the CMS admin.</p>
          <a href="/dashboard" className="text-blue-600 hover:text-blue-800 mt-4 inline-block">
            Return to Dashboard
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">CMS Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your AI Academy content and settings</p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'analytics', name: 'Analytics', icon: '📊' },
                { id: 'branding', name: 'Branding', icon: '🎨' },
                { id: 'content', name: 'Content Management', icon: '📝' },
                { id: 'testimonials', name: 'Testimonials', icon: '⭐' },
                { id: 'instructors', name: 'Instructors Page', icon: '👨‍🏫' },
                { id: 'webinars', name: 'Webinars', icon: '🎥' },
                { id: 'users', name: 'User Management', icon: '👥' },
                { id: 'contact', name: 'Contact Info', icon: '📍' },
                { id: 'social', name: 'Social Links', icon: '🔗' },
                { id: 'newsletter', name: 'Newsletter', icon: '📧' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'analytics' && analytics && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Platform Analytics</h2>
                
                {/* Overview Stats */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-blue-900">Total Courses</h3>
                    <p className="text-3xl font-bold text-blue-600">{analytics.overview.total_courses}</p>
                  </div>
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-green-900">Students</h3>
                    <p className="text-3xl font-bold text-green-600">{analytics.overview.total_students}</p>
                  </div>
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-purple-900">Instructors</h3>
                    <p className="text-3xl font-bold text-purple-600">{analytics.overview.total_instructors}</p>
                  </div>
                  <div className="bg-yellow-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-yellow-900">Enrollments</h3>
                    <p className="text-3xl font-bold text-yellow-600">{analytics.overview.total_enrollments}</p>
                  </div>
                  <div className="bg-indigo-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-indigo-900">Subscribers</h3>
                    <p className="text-3xl font-bold text-indigo-600">{analytics.overview.newsletter_subscribers}</p>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Users</h3>
                    <div className="space-y-3">
                      {analytics.recent_activity.new_users.slice(0, 5).map((user, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            user.role === 'instructor' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Growth</h3>
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 rounded-lg text-white">
                      <p className="text-lg">Total Platform Users</p>
                      <p className="text-3xl font-bold">
                        {analytics.overview.total_students + analytics.overview.total_instructors}
                      </p>
                      <p className="text-blue-100 mt-2">Growing your AI education community</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'branding' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Branding & Logo Management</h2>
                
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Branding</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Logo Preview */}
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-4">Logo Preview</h4>
                      <div className="border border-gray-300 rounded-lg p-6 bg-gray-50 text-center">
                        <img 
                          src={branding.logo_url || 'https://i.imgur.com/VQZvQxS.png'} 
                          alt="Current Logo" 
                          className="w-16 h-16 mx-auto object-contain mb-4"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'block';
                          }}
                        />
                        <div 
                          className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mx-auto flex items-center justify-center text-white font-bold mb-4" 
                          style={{display: 'none'}}
                        >
                          AI
                        </div>
                        <p className="text-gray-600 text-sm">Current Logo</p>
                      </div>
                    </div>

                    {/* Branding Settings */}
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-4">Update Branding</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Site Title</label>
                          <input
                            type="text"
                            value={editingBranding.site_title || ''}
                            onChange={(e) => setEditingBranding({...editingBranding, site_title: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="AI Academy"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Logo URL</label>
                          <input
                            type="url"
                            value={editingBranding.logo_url || ''}
                            onChange={(e) => setEditingBranding({...editingBranding, logo_url: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="https://example.com/logo.png"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Upload your logo to a service like Imgur, Cloudinary, or your own server, then paste the URL here.
                          </p>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h5 className="font-semibold text-blue-900 mb-2">Logo Guidelines:</h5>
                          <ul className="text-sm text-blue-800 space-y-1">
                            <li>• Recommended size: 40x40 pixels or square aspect ratio</li>
                            <li>• Supported formats: PNG, JPG, SVG</li>
                            <li>• Transparent background works best</li>
                            <li>• Keep file size under 100KB for best performance</li>
                          </ul>
                        </div>

                        <button
                          onClick={updateBranding}
                          className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-semibold"
                        >
                          Update Branding
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Upload Services</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Imgur</h4>
                      <p className="text-sm text-gray-600 mb-3">Free image hosting, no account required</p>
                      <a href="https://imgur.com/upload" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 text-sm">
                        Upload to Imgur →
                      </a>
                    </div>
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Cloudinary</h4>
                      <p className="text-sm text-gray-600 mb-3">Professional image hosting with CDN</p>
                      <a href="https://cloudinary.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 text-sm">
                        Visit Cloudinary →
                      </a>
                    </div>
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-2">GitHub</h4>
                      <p className="text-sm text-gray-600 mb-3">Host via GitHub repository</p>
                      <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 text-sm">
                        Use GitHub →
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'content' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Content Management</h2>
                
                {/* Quick Edit Forms */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Hero Section</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Main Title</label>
                        <input
                          type="text"
                          defaultValue="Master AI with Expert-Led Courses"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          onChange={(e) => setEditingContent({...editingContent, hero_title: e.target.value})}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Subtitle</label>
                        <textarea
                          defaultValue="Learn cutting-edge AI technologies from industry experts. Build real projects. Advance your career."
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          onChange={(e) => setEditingContent({...editingContent, hero_subtitle: e.target.value})}
                        />
                      </div>
                      <button
                        onClick={() => updateContent('hero_content', JSON.stringify(editingContent))}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Update Hero Section
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Newsletter Section</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Newsletter Title</label>
                        <input
                          type="text"
                          defaultValue="Stay Updated on AI Trends"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Newsletter Description</label>
                        <textarea
                          defaultValue="Get exclusive AI insights, free resources, and course updates delivered to your inbox"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                        />
                      </div>
                      <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Update Newsletter Section
                      </button>
                    </div>
                  </div>
                </div>

                {/* Existing Content */}
                {content.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Existing Content</h3>
                    <div className="space-y-3">
                      {content.map((item) => (
                        <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">{item.content_key}</p>
                              <p className="text-sm text-gray-600">{item.section} - {item.content_type}</p>
                            </div>
                            <span className="text-sm text-gray-500">
                              Updated: {new Date(item.updated_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'testimonials' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Testimonials Management</h2>
                
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Manage Testimonials</h3>
                    <div className="space-x-2">
                      {testimonials.length === 0 && (
                        <button
                          onClick={initDefaultTestimonials}
                          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                        >
                          Create Default Testimonials
                        </button>
                      )}
                      <button
                        onClick={() => setShowTestimonialForm(true)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Add New Testimonial
                      </button>
                    </div>
                  </div>

                  {/* Testimonials List */}
                  {testimonials.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No testimonials yet. Create some to show social proof on your homepage!</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {testimonials.map((testimonial) => (
                        <div key={testimonial.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start space-x-4">
                            {testimonial.avatar ? (
                              <img
                                src={testimonial.avatar}
                                alt={testimonial.name}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-blue-600 font-bold">
                                  {testimonial.name.charAt(0)}
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <h4 className="font-semibold text-gray-900 truncate">{testimonial.name}</h4>
                              <p className="text-sm text-gray-600 truncate">{testimonial.title}</p>
                              <p className="text-sm text-blue-600 truncate">{testimonial.company}</p>
                              <div className="flex items-center mt-1">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <span
                                    key={star}
                                    className={`text-sm ${star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                                  >
                                    ★
                                  </span>
                                ))}
                              </div>
                              <p className="text-xs text-gray-500 mt-2 line-clamp-2">
                                "{testimonial.testimonial}"
                              </p>
                            </div>
                          </div>
                          <div className="flex space-x-2 mt-3">
                            <button
                              onClick={() => editTestimonial(testimonial)}
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => deleteTestimonial(testimonial.id)}
                              className="text-red-600 hover:text-red-800 text-sm"
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Testimonial Form Modal */}
                {showTestimonialForm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-lg max-h-screen overflow-y-auto">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        {editingTestimonial ? 'Edit Testimonial' : 'Add New Testimonial'}
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <input
                              type="text"
                              value={testimonialFormData.name}
                              onChange={(e) => setTestimonialFormData({...testimonialFormData, name: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                            <input
                              type="text"
                              value={testimonialFormData.title}
                              onChange={(e) => setTestimonialFormData({...testimonialFormData, title: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                          <input
                            type="text"
                            value={testimonialFormData.company}
                            onChange={(e) => setTestimonialFormData({...testimonialFormData, company: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Avatar URL (optional)</label>
                          <input
                            type="url"
                            value={testimonialFormData.avatar}
                            onChange={(e) => setTestimonialFormData({...testimonialFormData, avatar: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                          <select
                            value={testimonialFormData.rating}
                            onChange={(e) => setTestimonialFormData({...testimonialFormData, rating: parseFloat(e.target.value)})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value={5.0}>5 Stars</option>
                            <option value={4.5}>4.5 Stars</option>
                            <option value={4.0}>4 Stars</option>
                            <option value={3.5}>3.5 Stars</option>
                            <option value={3.0}>3 Stars</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Testimonial</label>
                          <textarea
                            value={testimonialFormData.testimonial}
                            onChange={(e) => setTestimonialFormData({...testimonialFormData, testimonial: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            rows={4}
                            placeholder="What did they say about your course or service?"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                          <input
                            type="number"
                            value={testimonialFormData.order}
                            onChange={(e) => setTestimonialFormData({...testimonialFormData, order: parseInt(e.target.value)})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div className="flex space-x-4 pt-4">
                          <button
                            onClick={createOrUpdateTestimonial}
                            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-semibold"
                          >
                            {editingTestimonial ? 'Update Testimonial' : 'Create Testimonial'}
                          </button>
                          <button
                            onClick={() => {
                              setShowTestimonialForm(false);
                              setEditingTestimonial(null);
                            }}
                            className="flex-1 bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 font-semibold"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'instructors' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Instructors Page Content Management</h2>
                
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Instructors Page</h3>
                  
                  <div className="space-y-6">
                    {/* Hero Section */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="text-md font-semibold text-gray-800 mb-4">Hero Section</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Hero Title</label>
                          <input
                            type="text"
                            value={editingInstructorPage.hero_title || ''}
                            onChange={(e) => setEditingInstructorPage({...editingInstructorPage, hero_title: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Meet Our Expert Instructors"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Hero Subtitle</label>
                          <textarea
                            value={editingInstructorPage.hero_subtitle || ''}
                            onChange={(e) => setEditingInstructorPage({...editingInstructorPage, hero_subtitle: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            rows={3}
                            placeholder="Learn from industry leaders and AI pioneers who are shaping the future"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Call-to-Action Section */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="text-md font-semibold text-gray-800 mb-4">Call-to-Action Section</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">CTA Title</label>
                          <input
                            type="text"
                            value={editingInstructorPage.cta_title || ''}
                            onChange={(e) => setEditingInstructorPage({...editingInstructorPage, cta_title: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Ready to Learn from the Best?"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">CTA Subtitle</label>
                          <textarea
                            value={editingInstructorPage.cta_subtitle || ''}
                            onChange={(e) => setEditingInstructorPage({...editingInstructorPage, cta_subtitle: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            rows={2}
                            placeholder="Join thousands of students who are advancing their careers with expert-led AI courses"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">CTA Button Text</label>
                          <input
                            type="text"
                            value={editingInstructorPage.cta_button_text || ''}
                            onChange={(e) => setEditingInstructorPage({...editingInstructorPage, cta_button_text: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Get Started"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Preview */}
                    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <h4 className="text-md font-semibold text-gray-800 mb-4">Preview</h4>
                      <div className="space-y-4">
                        <div className="text-center p-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg">
                          <h1 className="text-2xl font-bold mb-2">{editingInstructorPage.hero_title || 'Meet Our Expert Instructors'}</h1>
                          <p className="text-blue-100">{editingInstructorPage.hero_subtitle || 'Learn from industry leaders and AI pioneers who are shaping the future'}</p>
                        </div>
                        <div className="text-center p-6 bg-blue-600 text-white rounded-lg">
                          <h2 className="text-xl font-bold mb-2">{editingInstructorPage.cta_title || 'Ready to Learn from the Best?'}</h2>
                          <p className="text-blue-100 mb-4">{editingInstructorPage.cta_subtitle || 'Join thousands of students who are advancing their careers with expert-led AI courses'}</p>
                          <button className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold">
                            {editingInstructorPage.cta_button_text || 'Get Started'}
                          </button>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={updateInstructorPage}
                      className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-semibold"
                    >
                      Update Instructors Page
                    </button>
                  </div>
                </div>

                {/* Instructor Profiles Management */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Manage Instructor Profiles</h3>
                    <div className="space-x-2">
                      {instructorProfiles.length === 0 && (
                        <button
                          onClick={initDefaultInstructors}
                          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                        >
                          Create Default Instructors
                        </button>
                      )}
                      <button
                        onClick={() => setShowProfileForm(true)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Add New Instructor
                      </button>
                    </div>
                  </div>

                  {/* Instructor Profiles List */}
                  {instructorProfiles.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No instructor profiles yet. Create some to populate your instructors page!</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {instructorProfiles.map((profile) => (
                        <div key={profile.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start space-x-4">
                            <img
                              src={profile.avatar}
                              alt={profile.name}
                              className="w-16 h-16 rounded-full object-cover"
                            />
                            <div className="flex-1">
                              <h4 className="font-semibold text-gray-900">{profile.name}</h4>
                              <p className="text-sm text-blue-600">{profile.title}</p>
                              <p className="text-sm text-gray-600">{profile.company}</p>
                              <div className="flex items-center space-x-2 mt-2">
                                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                  ★ {profile.rating}
                                </span>
                                <span className="text-xs text-gray-500">{profile.students} students</span>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => editProfile(profile)}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => deleteProfile(profile.id)}
                                className="text-red-600 hover:text-red-800"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Instructor Profile Form Modal */}
                {showProfileForm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        {editingProfile ? 'Edit Instructor Profile' : 'Add New Instructor Profile'}
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <input
                              type="text"
                              value={profileFormData.name}
                              onChange={(e) => setProfileFormData({...profileFormData, name: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                            <input
                              type="text"
                              value={profileFormData.title}
                              onChange={(e) => setProfileFormData({...profileFormData, title: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Avatar URL</label>
                          <input
                            type="url"
                            value={profileFormData.avatar}
                            onChange={(e) => setProfileFormData({...profileFormData, avatar: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                          <textarea
                            value={profileFormData.bio}
                            onChange={(e) => setProfileFormData({...profileFormData, bio: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            rows={3}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                            <input
                              type="text"
                              value={profileFormData.company}
                              onChange={(e) => setProfileFormData({...profileFormData, company: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Education</label>
                            <input
                              type="text"
                              value={profileFormData.education}
                              onChange={(e) => setProfileFormData({...profileFormData, education: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                            <input
                              type="text"
                              value={profileFormData.experience}
                              onChange={(e) => setProfileFormData({...profileFormData, experience: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="e.g., 10+ years"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Students</label>
                            <input
                              type="text"
                              value={profileFormData.students}
                              onChange={(e) => setProfileFormData({...profileFormData, students: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="e.g., 10,000+"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                            <input
                              type="number"
                              step="0.1"
                              min="1"
                              max="5"
                              value={profileFormData.rating}
                              onChange={(e) => setProfileFormData({...profileFormData, rating: parseFloat(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Expertise (comma-separated)</label>
                          <input
                            type="text"
                            value={Array.isArray(profileFormData.expertise) ? profileFormData.expertise.join(', ') : ''}
                            onChange={(e) => setProfileFormData({
                              ...profileFormData, 
                              expertise: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g., Machine Learning, Deep Learning, Computer Vision"
                          />
                        </div>

                        <div className="flex space-x-4 pt-4">
                          <button
                            onClick={createOrUpdateProfile}
                            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-semibold"
                          >
                            {editingProfile ? 'Update Profile' : 'Create Profile'}
                          </button>
                          <button
                            onClick={() => {
                              setShowProfileForm(false);
                              setEditingProfile(null);
                            }}
                            className="flex-1 bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 font-semibold"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'webinars' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Webinar Management</h2>
                
                {/* Create Webinar Form */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Webinar</h3>
                  <form onSubmit={createWebinar} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <input
                          type="text"
                          required
                          value={webinarForm.title}
                          onChange={(e) => setWebinarForm({...webinarForm, title: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Date & Time</label>
                        <input
                          type="datetime-local"
                          required
                          value={webinarForm.date_time}
                          onChange={(e) => setWebinarForm({...webinarForm, date_time: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                      <textarea
                        required
                        value={webinarForm.description}
                        onChange={(e) => setWebinarForm({...webinarForm, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                        <input
                          type="number"
                          value={webinarForm.duration_minutes}
                          onChange={(e) => setWebinarForm({...webinarForm, duration_minutes: parseInt(e.target.value)})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Max Attendees</label>
                        <input
                          type="number"
                          value={webinarForm.max_attendees}
                          onChange={(e) => setWebinarForm({...webinarForm, max_attendees: parseInt(e.target.value)})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Registration Link</label>
                        <input
                          type="url"
                          value={webinarForm.registration_link}
                          onChange={(e) => setWebinarForm({...webinarForm, registration_link: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    
                    <button
                      type="submit"
                      className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                    >
                      Create Webinar
                    </button>
                  </form>
                </div>

                {/* Existing Webinars */}
                {webinars.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Scheduled Webinars</h3>
                    <div className="space-y-4">
                      {webinars.map((webinar) => (
                        <div key={webinar.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold text-gray-900">{webinar.title}</h4>
                              <p className="text-gray-600 mt-1">{webinar.description}</p>
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                <span>📅 {new Date(webinar.date_time).toLocaleString()}</span>
                                <span>⏱️ {webinar.duration_minutes} min</span>
                                <span>👥 Max {webinar.max_attendees}</span>
                              </div>
                            </div>
                            <span className={`px-3 py-1 text-sm rounded-full ${
                              webinar.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {webinar.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'users' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">User Management</h2>
                
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              user.role === 'instructor' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {user.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(user.created_at).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">User Management</h2>
                  <button
                    onClick={() => {
                      setShowUserForm(true);
                      setEditingUser(null);
                      setUserFormData({ name: '', email: '', role: 'student', password: '' });
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    Add New User
                  </button>
                </div>

                {/* User Form Modal */}
                {(showUserForm || editingUser) && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
                      <h3 className="text-lg font-semibold mb-4">
                        {editingUser ? 'Edit User' : 'Add New User'}
                      </h3>
                      <form onSubmit={editingUser ? updateUser : createUser} className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Name</label>
                          <input
                            type="text"
                            value={userFormData.name}
                            onChange={(e) => setUserFormData({ ...userFormData, name: e.target.value })}
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <input
                            type="email"
                            value={userFormData.email}
                            onChange={(e) => setUserFormData({ ...userFormData, email: e.target.value })}
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Role</label>
                          <select
                            value={userFormData.role}
                            onChange={(e) => setUserFormData({ ...userFormData, role: e.target.value })}
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                          >
                            <option value="student">Student</option>
                            <option value="instructor">Instructor</option>
                            <option value="admin">Admin</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Password {editingUser && '(leave blank to keep current)'}
                          </label>
                          <input
                            type="password"
                            value={userFormData.password}
                            onChange={(e) => setUserFormData({ ...userFormData, password: e.target.value })}
                            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                            required={!editingUser}
                          />
                        </div>
                        <div className="flex space-x-3">
                          <button
                            type="submit"
                            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                          >
                            {editingUser ? 'Update User' : 'Create User'}
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowUserForm(false);
                              setEditingUser(null);
                              setUserFormData({ name: '', email: '', role: 'student', password: '' });
                            }}
                            className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                          >
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              user.role === 'admin' ? 'bg-red-100 text-red-800' :
                              user.role === 'instructor' ? 'bg-blue-100 text-blue-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {user.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(user.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                            <button
                              onClick={() => {
                                setEditingUser(user);
                                setUserFormData({
                                  name: user.name,
                                  email: user.email,
                                  role: user.role,
                                  password: ''
                                });
                              }}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => deleteUser(user.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Contact Information Management</h2>
                
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium mb-4">Edit Contact Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Company Name</label>
                      <input
                        type="text"
                        value={editingContactInfo.company_name || ''}
                        onChange={(e) => setEditingContactInfo({ ...editingContactInfo, company_name: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <input
                        type="text"
                        value={editingContactInfo.phone || ''}
                        onChange={(e) => setEditingContactInfo({ ...editingContactInfo, phone: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        value={editingContactInfo.email || ''}
                        onChange={(e) => setEditingContactInfo({ ...editingContactInfo, email: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Business Hours</label>
                      <input
                        type="text"
                        value={editingContactInfo.business_hours || ''}
                        onChange={(e) => setEditingContactInfo({ ...editingContactInfo, business_hours: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Address</label>
                      <textarea
                        value={editingContactInfo.address || ''}
                        onChange={(e) => setEditingContactInfo({ ...editingContactInfo, address: e.target.value })}
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      />
                    </div>
                  </div>
                  <div className="mt-6">
                    <button
                      onClick={updateContactInfo}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Update Contact Information
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'social' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Social Media Links Management</h2>
                
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium mb-4">Edit Social Media Links</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Facebook URL</label>
                      <input
                        type="url"
                        value={editingSocialLinks.facebook || ''}
                        onChange={(e) => setEditingSocialLinks({ ...editingSocialLinks, facebook: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="https://facebook.com/yourpage"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">YouTube URL</label>
                      <input
                        type="url"
                        value={editingSocialLinks.youtube || ''}
                        onChange={(e) => setEditingSocialLinks({ ...editingSocialLinks, youtube: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="https://youtube.com/yourchannel"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">LinkedIn URL</label>
                      <input
                        type="url"
                        value={editingSocialLinks.linkedin || ''}
                        onChange={(e) => setEditingSocialLinks({ ...editingSocialLinks, linkedin: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="https://linkedin.com/company/yourcompany"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">X (Twitter) URL</label>
                      <input
                        type="url"
                        value={editingSocialLinks.twitter || ''}
                        onChange={(e) => setEditingSocialLinks({ ...editingSocialLinks, twitter: e.target.value })}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="https://x.com/youraccount"
                      />
                    </div>
                  </div>
                  <div className="mt-6">
                    <button
                      onClick={updateSocialLinks}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Update Social Links
                    </button>
                  </div>
                </div>

                {/* Preview section */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium mb-4">Preview</h3>
                  <div className="flex space-x-4">
                    {editingSocialLinks.facebook && (
                      <a
                        href={editingSocialLinks.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        🔗 Facebook
                      </a>
                    )}
                    {editingSocialLinks.youtube && (
                      <a
                        href={editingSocialLinks.youtube}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-red-600 hover:text-red-800"
                      >
                        🎥 YouTube
                      </a>
                    )}
                    {editingSocialLinks.linkedin && (
                      <a
                        href={editingSocialLinks.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-700 hover:text-blue-900"
                      >
                        💼 LinkedIn
                      </a>
                    )}
                    {editingSocialLinks.twitter && (
                      <a
                        href={editingSocialLinks.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-800 hover:text-black"
                      >
                        🐦 X (Twitter)
                      </a>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'newsletter' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Newsletter Management</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-blue-900">Total Subscribers</h3>
                    <p className="text-3xl font-bold text-blue-600">{subscribers.length}</p>
                  </div>
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-green-900">Active Subscribers</h3>
                    <p className="text-3xl font-bold text-green-600">{subscribers.filter(s => s.is_active).length}</p>
                  </div>
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-purple-900">Growth Rate</h3>
                    <p className="text-3xl font-bold text-purple-600">+12%</p>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribed</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {subscribers.map((subscriber) => (
                        <tr key={subscriber.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subscriber.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{subscriber.name || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(subscriber.subscribed_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              subscriber.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {subscriber.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Instructors Page Component
const InstructorsPage = () => {
  const [instructors, setInstructors] = useState([]);
  const [pageContent, setPageContent] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInstructorsData();
  }, []);

  const fetchInstructorsData = async () => {
    try {
      const [instructorsRes, contentRes] = await Promise.all([
        axios.get(`${API}/instructor-profiles`),
        axios.get(`${API}/instructor-page-content`)
      ]);
      
      setInstructors(instructorsRes.data);
      setPageContent(contentRes.data);
    } catch (error) {
      console.error('Error fetching instructors data:', error);
      // Fallback to default content
      setPageContent({
        hero_title: "Meet Our Expert Instructors",
        hero_subtitle: "Learn from industry leaders and AI pioneers who are shaping the future",
        cta_title: "Ready to Learn from the Best?",
        cta_subtitle: "Join thousands of students who are advancing their careers with expert-led AI courses",
        cta_button_text: "Get Started"
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`text-lg ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {pageContent.hero_title || "Meet Our Expert Instructors"}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              {pageContent.hero_subtitle || "Learn from industry leaders and AI pioneers who are shaping the future"}
            </p>
          </div>
        </div>
      </div>

      {/* Instructors Grid */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {instructors.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No Instructors Available</h2>
              <p className="text-gray-600">Our instructor profiles will be displayed here once they are added.</p>
            </div>
          ) : (
            <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
              {instructors.map((instructor) => (
                <div key={instructor.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                {/* Profile Image & Basic Info */}
                <div className="relative">
                  <div className="h-32 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                  <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
                    <img
                      src={instructor.avatar}
                      alt={instructor.name}
                      className="w-24 h-24 rounded-full border-4 border-white shadow-lg object-cover"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="pt-16 p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{instructor.name}</h3>
                    <p className="text-blue-600 font-semibold mb-2">{instructor.title}</p>
                    <p className="text-gray-600 text-sm">{instructor.company}</p>
                    <p className="text-gray-500 text-sm">{instructor.education}</p>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    {renderStars(Math.round(instructor.rating))}
                    <span className="text-lg font-semibold text-gray-900">{instructor.rating}</span>
                    <span className="text-gray-500">({instructor.students} students)</span>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-6 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{instructor.courses}</div>
                      <div className="text-sm text-gray-600">Courses</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{instructor.students}</div>
                      <div className="text-sm text-gray-600">Students</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">{instructor.experience}</div>
                      <div className="text-sm text-gray-600">Experience</div>
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-gray-700 text-sm mb-6 line-clamp-3">{instructor.bio}</p>

                  {/* Expertise Tags */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Expertise:</h4>
                    <div className="flex flex-wrap gap-2">
                      {instructor.expertise.map((skill) => (
                        <span
                          key={skill}
                          className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <button 
                    onClick={() => {
                      // Navigate to home page and scroll to courses
                      window.location.href = '/#courses-section';
                    }}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                  >
                    View Courses
                  </button>
                </div>
              </div>
            ))}
            </div>
          )}
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            {pageContent.cta_title || "Ready to Learn from the Best?"}
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            {pageContent.cta_subtitle || "Join thousands of students who are advancing their careers with expert-led AI courses"}
          </p>
          <div className="space-x-4">
            <a
              href="/"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200"
            >
              Browse Courses
            </a>
            <a
              href="/register"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-200"
            >
              {pageContent.cta_button_text || "Get Started"}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

// Course Creation Component
const CreateCoursePage = () => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'AI & Technology',
    duration_minutes: '',
    price: 0.0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.post(`${API}/courses`, formData);
      alert('Course created successfully!');
      window.location.href = '/dashboard';
    } catch (error) {
      setError(error.response?.data?.detail || 'Course creation failed');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  if (user?.role !== 'instructor') {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow px-6 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Create New Course</h1>
            <p className="text-gray-600 mt-2">Share your knowledge and help others learn AI</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Course Title
              </label>
              <input
                id="title"
                name="title"
                type="text"
                required
                value={formData.title}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Introduction to Machine Learning"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Course Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                required
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe what students will learn in this course..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="AI & Technology">AI & Technology</option>
                  <option value="Machine Learning">Machine Learning</option>
                  <option value="Deep Learning">Deep Learning</option>
                  <option value="Data Science">Data Science</option>
                  <option value="Programming">Programming</option>
                </select>
              </div>

              <div>
                <label htmlFor="duration_minutes" className="block text-sm font-medium text-gray-700 mb-2">
                  Duration (minutes)
                </label>
                <input
                  id="duration_minutes"
                  name="duration_minutes"
                  type="number"
                  value={formData.duration_minutes}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., 180"
                />
              </div>
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                Price (USD)
              </label>
              <input
                id="price"
                name="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00 for free course"
              />
            </div>

            <div className="flex space-x-4">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Course'}
              </button>
              <a
                href="/dashboard"
                className="flex-1 text-center bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition duration-200"
              >
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Course Management Component
const CoursePage = () => {
  const { user } = useAuth();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [progress, setProgress] = useState(null);
  const [discussions, setDiscussions] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showLessonForm, setShowLessonForm] = useState(false);
  const [showDiscussionForm, setShowDiscussionForm] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [activeTab, setActiveTab] = useState('lessons');
  const [lessonFormData, setLessonFormData] = useState({
    title: '',
    description: '',
    type: 'text',
    content: '',
    duration_minutes: ''
  });
  const [discussionText, setDiscussionText] = useState('');
  const [reviewData, setReviewData] = useState({
    rating: 5,
    review: ''
  });

  // Get course ID from URL
  const courseId = window.location.pathname.split('/').pop();

  useEffect(() => {
    fetchCourseData();
  }, [courseId]);

  const fetchCourseData = async () => {
    try {
      const promises = [
        axios.get(`${API}/courses/${courseId}`),
        axios.get(`${API}/courses/${courseId}/lessons`),
        axios.get(`${API}/courses/${courseId}/reviews`)
      ];

      // Add discussions if user is enrolled or instructor
      if (user) {
        promises.push(axios.get(`${API}/courses/${courseId}/discussions`).catch(() => ({ data: [] })));
      }
      
      const responses = await Promise.all(promises);
      const [courseResponse, lessonsResponse, reviewsResponse, discussionsResponse] = responses;
      
      setCourse(courseResponse.data);
      setLessons(lessonsResponse.data);
      setReviews(reviewsResponse.data);
      if (discussionsResponse) {
        setDiscussions(discussionsResponse.data);
      }

      // If student, fetch progress
      if (user?.role === 'student') {
        try {
          const progressResponse = await axios.get(`${API}/courses/${courseId}/progress`);
          setProgress(progressResponse.data);
        } catch (error) {
          console.log('Not enrolled in course');
        }
      }
    } catch (error) {
      console.error('Error fetching course data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    try {
      await axios.post(`${API}/courses/${courseId}/enroll`);
      alert('Enrolled successfully!');
      fetchCourseData();
    } catch (error) {
      alert(error.response?.data?.detail || 'Enrollment failed');
    }
  };

  const handlePublishCourse = async () => {
    try {
      await axios.put(`${API}/courses/${courseId}/publish`);
      alert('Course published successfully!');
      fetchCourseData();
    } catch (error) {
      alert('Failed to publish course');
    }
  };

  const handleCreateLesson = async (e) => {
    e.preventDefault();
    try {
      const lessonData = {
        ...lessonFormData,
        order: lessons.length + 1,
        duration_minutes: parseInt(lessonFormData.duration_minutes) || 0
      };
      
      await axios.post(`${API}/courses/${courseId}/lessons`, lessonData);
      alert('Lesson created successfully!');
      setShowLessonForm(false);
      setLessonFormData({
        title: '',
        description: '',
        type: 'text',
        content: '',
        duration_minutes: ''
      });
      fetchCourseData();
    } catch (error) {
      alert('Failed to create lesson');
    }
  };

  const handleCompleteLesson = async (lessonId) => {
    try {
      await axios.post(`${API}/lessons/${lessonId}/complete`);
      alert('Lesson completed!');
      fetchCourseData();
    } catch (error) {
      alert('Failed to mark lesson as complete');
    }
  };

  const handleCreateDiscussion = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/courses/${courseId}/discussions`, { message: discussionText });
      setDiscussionText('');
      setShowDiscussionForm(false);
      fetchCourseData();
    } catch (error) {
      alert('Failed to post discussion');
    }
  };

  const handleCreateReview = async (e) => {
    e.preventDefault();
    try {
      await axios.post(`${API}/courses/${courseId}/reviews`, reviewData);
      setReviewData({ rating: 5, review: '' });
      setShowReviewForm(false);
      alert('Review submitted successfully!');
      fetchCourseData();
    } catch (error) {
      alert(error.response?.data?.detail || 'Failed to submit review');
    }
  };

  const renderStars = (rating, interactive = false, onChange = null) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={!interactive}
            onClick={() => interactive && onChange && onChange(star)}
            className={`text-xl ${star <= rating ? 'text-yellow-400' : 'text-gray-300'} ${
              interactive ? 'hover:text-yellow-500 cursor-pointer' : 'cursor-default'
            }`}
          >
            ★
          </button>
        ))}
      </div>
    );
  };

  const getAverageRating = () => {
    if (reviews.length === 0) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Course not found</h1>
          <a href="/dashboard" className="text-blue-600 hover:text-blue-800">Return to Dashboard</a>
        </div>
      </div>
    );
  }

  const isInstructor = user?.role === 'instructor' && course.instructor_id === user.id;
  const isStudent = user?.role === 'student';
  const isEnrolled = progress !== null;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Header */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-8">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{course.title}</h1>
                <p className="text-gray-600 mb-4">{course.description}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                  <span>By {course.instructor_name}</span>
                  <span>•</span>
                  <span>{course.student_count} students</span>
                  <span>•</span>
                  <span>{course.category}</span>
                  {course.duration_minutes && (
                    <>
                      <span>•</span>
                      <span>{course.duration_minutes} minutes</span>
                    </>
                  )}
                </div>
                
                {/* Course Rating */}
                {reviews.length > 0 && (
                  <div className="flex items-center space-x-2 mb-4">
                    {renderStars(Math.round(getAverageRating()))}
                    <span className="text-lg font-semibold text-gray-900">{getAverageRating()}</span>
                    <span className="text-gray-500">({reviews.length} reviews)</span>
                  </div>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {course.price === 0 ? 'Free' : `$${course.price}`}
                </div>
                <div className={`text-sm px-3 py-1 rounded-full ${
                  course.is_published 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {course.is_published ? 'Published' : 'Draft'}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              {isInstructor && (
                <>
                  {!course.is_published && (
                    <button
                      onClick={handlePublishCourse}
                      className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                    >
                      Publish Course
                    </button>
                  )}
                  <button
                    onClick={() => setShowLessonForm(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Add Lesson
                  </button>
                </>
              )}
              
              {isStudent && !isEnrolled && course.is_published && (
                <button
                  onClick={handleEnroll}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Enroll Now
                </button>
              )}
              
              {isEnrolled && (
                <div className="bg-green-100 text-green-800 px-4 py-2 rounded-md">
                  Enrolled - {Math.round(progress?.completion_percentage || 0)}% Complete
                </div>
              )}
            </div>
          </div>
          
          {/* Navigation Tabs */}
          <div className="border-t border-gray-200">
            <nav className="flex space-x-8 px-6">
              {['lessons', 'discussions', 'reviews'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab}
                  {tab === 'lessons' && ` (${lessons.length})`}
                  {tab === 'discussions' && ` (${discussions.length})`}
                  {tab === 'reviews' && ` (${reviews.length})`}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Lesson Creation Form */}
        {showLessonForm && isInstructor && (
          <div className="bg-white rounded-lg shadow mb-8 p-6">
            <h2 className="text-xl font-bold mb-4">Create New Lesson</h2>
            <form onSubmit={handleCreateLesson} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Lesson Title</label>
                <input
                  type="text"
                  required
                  value={lessonFormData.title}
                  onChange={(e) => setLessonFormData({...lessonFormData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={lessonFormData.description}
                  onChange={(e) => setLessonFormData({...lessonFormData, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <select
                    value={lessonFormData.type}
                    onChange={(e) => setLessonFormData({...lessonFormData, type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="text">Text Content</option>
                    <option value="video">Video</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                  <input
                    type="number"
                    value={lessonFormData.duration_minutes}
                    onChange={(e) => setLessonFormData({...lessonFormData, duration_minutes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {lessonFormData.type === 'video' ? 'Video URL (YouTube)' : 'Content'}
                </label>
                {lessonFormData.type === 'video' ? (
                  <input
                    type="url"
                    required
                    value={lessonFormData.content}
                    onChange={(e) => setLessonFormData({...lessonFormData, content: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://www.youtube.com/watch?v=..."
                  />
                ) : (
                  <textarea
                    required
                    value={lessonFormData.content}
                    onChange={(e) => setLessonFormData({...lessonFormData, content: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    rows={6}
                    placeholder="Enter lesson content..."
                  />
                )}
              </div>
              
              <div className="flex space-x-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Create Lesson
                </button>
                <button
                  type="button"
                  onClick={() => setShowLessonForm(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Content Sections */}
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'lessons' && (
            <div className="p-6">
              {lessons.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No lessons yet.</p>
                  {isInstructor && (
                    <button
                      onClick={() => setShowLessonForm(true)}
                      className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                      Add First Lesson
                    </button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {lessons.map((lesson, index) => {
                    const lessonProgress = progress?.lessons?.find(l => l.lesson.id === lesson.id);
                    const isCompleted = lessonProgress?.completed || false;
                    
                    return (
                      <div key={lesson.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <span className="text-sm text-gray-500">Lesson {index + 1}</span>
                              {isCompleted && (
                                <span className="text-green-600">✓</span>
                              )}
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">{lesson.title}</h3>
                            {lesson.description && (
                              <p className="text-gray-600 mt-1">{lesson.description}</p>
                            )}
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-2">
                              <span className="capitalize">{lesson.type}</span>
                              {lesson.duration_minutes && (
                                <>
                                  <span>•</span>
                                  <span>{lesson.duration_minutes} minutes</span>
                                </>
                              )}
                            </div>
                          </div>
                          
                          {isEnrolled && !isCompleted && (
                            <button
                              onClick={() => handleCompleteLesson(lesson.id)}
                              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                            >
                              Mark Complete
                            </button>
                          )}
                        </div>
                        
                        {/* Lesson Content Preview */}
                        {lesson.type === 'video' && lesson.content && (
                          <div className="mt-4">
                            <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                              <span className="text-gray-500">Video: {lesson.content}</span>
                            </div>
                          </div>
                        )}
                        
                        {lesson.type === 'text' && lesson.content && (
                          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: lesson.content.substring(0, 300) + '...' }} />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {activeTab === 'discussions' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Course Discussions</h3>
                {(isEnrolled || isInstructor) && (
                  <button
                    onClick={() => setShowDiscussionForm(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Start Discussion
                  </button>
                )}
              </div>

              {showDiscussionForm && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <form onSubmit={handleCreateDiscussion}>
                    <textarea
                      value={discussionText}
                      onChange={(e) => setDiscussionText(e.target.value)}
                      placeholder="Share your thoughts, ask questions, or help fellow students..."
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={4}
                      required
                    />
                    <div className="flex space-x-3 mt-3">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Post Discussion
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowDiscussionForm(false)}
                        className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {discussions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No discussions yet. Start the conversation!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {discussions.map((discussion) => (
                    <div key={discussion.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">
                            {discussion.user_name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-semibold text-gray-900">{discussion.user_name}</span>
                            {discussion.is_instructor && (
                              <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                                Instructor
                              </span>
                            )}
                            <span className="text-sm text-gray-500">
                              {new Date(discussion.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-gray-700">{discussion.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Course Reviews</h3>
                {isEnrolled && (
                  <button
                    onClick={() => setShowReviewForm(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Write Review
                  </button>
                )}
              </div>

              {showReviewForm && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <form onSubmit={handleCreateReview}>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                      {renderStars(reviewData.rating, true, (rating) => 
                        setReviewData({ ...reviewData, rating })
                      )}
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Review</label>
                      <textarea
                        value={reviewData.review}
                        onChange={(e) => setReviewData({ ...reviewData, review: e.target.value })}
                        placeholder="Share your experience with this course..."
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        rows={4}
                        required
                      />
                    </div>
                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Submit Review
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowReviewForm(false)}
                        className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {reviews.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No reviews yet. Be the first to review this course!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-semibold">
                            {review.user_name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="font-semibold text-gray-900">{review.user_name}</span>
                            <span className="text-sm text-gray-500">
                              {new Date(review.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 mb-2">
                            {renderStars(review.rating)}
                            <span className="text-sm text-gray-600">({review.rating}/5)</span>
                          </div>
                          <p className="text-gray-700">{review.review}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Main App Component
function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Navigation />
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/instructors" 
              element={<InstructorsPage />} 
            />
            <Route 
              path="/admin" 
              element={
                <ProtectedRoute>
                  <CMSAdminPage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/create-course" 
              element={
                <ProtectedRoute>
                  <CreateCoursePage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/course/:id" 
              element={
                <ProtectedRoute>
                  <CoursePage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contact" 
              element={<ContactPage />} 
            />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
