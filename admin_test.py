import requests
import json
import time
import os
import sys
import random
import string
from pprint import pprint

# Get the backend URL from environment or use a default
BACKEND_URL = "https://10fab1d6-02b0-4d8b-826a-6ef585309ae8.preview.emergentagent.com"
API_URL = f"{BACKEND_URL}/api"

def print_section(title):
    print("\n" + "="*80)
    print(f" {title} ".center(80, "="))
    print("="*80)

def print_test_result(test_name, success, details=None):
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"{status} - {test_name}")
    if details:
        print(f"  Details: {details}")

def print_response(response):
    try:
        print(f"Status Code: {response.status_code}")
        print("Response:")
        pprint(response.json())
    except:
        print(f"Raw response: {response.text}")

# Test results
test_results = {
    "super_admin_creation": False,
    "admin_login": False,
    "user_management": {
        "list_users": False,
        "create_user": False,
        "update_user": False,
        "delete_user": False
    },
    "contact_info": {
        "get": False,
        "update": False
    },
    "social_links": {
        "get": False,
        "update": False
    },
    "role_based_access": {
        "non_admin_access_denied": False,
        "admin_only_endpoints": False
    }
}

# Store tokens and user IDs
tokens = {
    "admin": None,
    "instructor": None,
    "student": None
}

user_ids = {
    "instructor": None,
    "student": None
}

# Phase 1: Authentication & Super Admin Setup
print_section("Phase 1: Authentication & Super Admin Setup")

# Test 1: Create Super Admin
print("\n1. Testing Super Admin Creation")
admin_data = {
    "email": "<EMAIL>",
    "password": "adminpassword123",
    "name": "Super Admin"
}

response = requests.post(f"{API_URL}/auth/create-super-admin", json=admin_data)
print_response(response)

if response.status_code == 200:
    test_results["super_admin_creation"] = True
    print_test_result("Create Super Admin", True)
elif response.status_code == 400 and "Super admin already exists" in response.text:
    # This is also acceptable if super admin already exists
    test_results["super_admin_creation"] = True
    print_test_result("Create Super Admin", True, "Super admin already exists")
else:
    print_test_result("Create Super Admin", False)

# Test 2: Try creating another super admin (should fail)
print("\n2. Testing Duplicate Super Admin Creation (should fail)")
admin_data2 = {
    "email": "<EMAIL>",
    "password": "adminpassword456",
    "name": "Second Admin"
}

response = requests.post(f"{API_URL}/auth/create-super-admin", json=admin_data2)
print_response(response)

if response.status_code == 400 and "Super admin already exists" in response.text:
    print_test_result("Prevent Duplicate Super Admin", True)
else:
    print_test_result("Prevent Duplicate Super Admin", False)

# Test 3: Admin Login
print("\n3. Testing Admin Login")

# Try different admin credentials
admin_credentials = [
    {"email": "<EMAIL>", "password": "adminpassword123"},
    {"email": "<EMAIL>", "password": "superadmin123"},
    {"email": "<EMAIL>", "password": "admin123"},
    {"email": "<EMAIL>", "password": "superadmin123"}
]

admin_login_success = False
for creds in admin_credentials:
    print(f"\nTrying login with: {creds['email']}")
    response = requests.post(f"{API_URL}/auth/login", json=creds)
    print_response(response)
    
    if response.status_code == 200:
        admin_token = response.json().get("access_token")
        tokens["admin"] = admin_token
        test_results["admin_login"] = True
        print_test_result("Admin Login", True, f"Logged in as {creds['email']}")
        admin_login_success = True
        break

if not admin_login_success:
    print_test_result("Admin Login", False)
    print("Cannot proceed with admin-only tests without admin token")
    sys.exit(1)

# Phase 2: User Management (Admin Only)
print_section("Phase 2: User Management (Admin Only)")

# Test 4: List all users
print("\n4. Testing List All Users")
headers = {"Authorization": f"Bearer {tokens['admin']}"}
response = requests.get(f"{API_URL}/admin/users", headers=headers)
print_response(response)

if response.status_code == 200:
    test_results["user_management"]["list_users"] = True
    print_test_result("List All Users", True)
else:
    print_test_result("List All Users", False)

# Test 5: Create new users
print("\n5. Testing Create New Users")

# Create instructor
random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
instructor_data = {
    "email": f"instructor_{random_suffix}@test.com",
    "password": f"instructor123",
    "name": f"Test Instructor {random_suffix}",
    "role": "instructor"
}

response = requests.post(f"{API_URL}/admin/users", json=instructor_data, headers=headers)
print_response(response)

if response.status_code == 200:
    user_ids["instructor"] = response.json().get("id")
    instructor_email = instructor_data["email"]
    instructor_password = instructor_data["password"]
    print_test_result("Create Instructor User", True)
else:
    print_test_result("Create Instructor User", False)

# Create student
random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
student_data = {
    "email": f"student_{random_suffix}@test.com",
    "password": f"student123",
    "name": f"Test Student {random_suffix}",
    "role": "student"
}

response = requests.post(f"{API_URL}/admin/users", json=student_data, headers=headers)
print_response(response)

if response.status_code == 200:
    user_ids["student"] = response.json().get("id")
    test_results["user_management"]["create_user"] = True
    print_test_result("Create Student User", True)
else:
    print_test_result("Create Student User", False)

# Test 6: Update existing user
print("\n6. Testing Update User")
if user_ids["instructor"]:
    update_data = {
        "name": "Updated Instructor Name",
        "bio": "This is an updated instructor bio"
    }
    
    response = requests.put(
        f"{API_URL}/admin/users/{user_ids['instructor']}", 
        json=update_data, 
        headers=headers
    )
    print_response(response)
    
    if response.status_code == 200:
        test_results["user_management"]["update_user"] = True
        print_test_result("Update User", True)
    else:
        print_test_result("Update User", False)
else:
    print("Skipping update user test - no instructor ID available")

# Test 7: Delete user
print("\n7. Testing Delete User")
if user_ids["student"]:
    response = requests.delete(
        f"{API_URL}/admin/users/{user_ids['student']}", 
        headers=headers
    )
    print_response(response)
    
    if response.status_code == 200:
        test_results["user_management"]["delete_user"] = True
        print_test_result("Delete User", True)
    else:
        print_test_result("Delete User", False)
else:
    print("Skipping delete user test - no student ID available")

# Phase 3: Contact & Social Links Management
print_section("Phase 3: Contact & Social Links Management")

# Test 8: Get contact information
print("\n8. Testing Get Contact Information")
response = requests.get(f"{API_URL}/contact-info")
print_response(response)

if response.status_code == 200:
    test_results["contact_info"]["get"] = True
    print_test_result("Get Contact Info", True)
else:
    print_test_result("Get Contact Info", False)

# Test 9: Update contact information (admin only)
print("\n9. Testing Update Contact Information")
contact_data = {
    "company_name": "Updated AI Academy",
    "phone": "+****************",
    "email": "<EMAIL>"
}

response = requests.put(
    f"{API_URL}/admin/contact-info", 
    json=contact_data, 
    headers=headers
)
print_response(response)

if response.status_code == 200:
    test_results["contact_info"]["update"] = True
    print_test_result("Update Contact Info", True)
else:
    print_test_result("Update Contact Info", False)

# Test 10: Get social media links
print("\n10. Testing Get Social Links")
response = requests.get(f"{API_URL}/social-links")
print_response(response)

if response.status_code == 200:
    test_results["social_links"]["get"] = True
    print_test_result("Get Social Links", True)
else:
    print_test_result("Get Social Links", False)

# Test 11: Update social media links (admin only)
print("\n11. Testing Update Social Links")
social_data = {
    "facebook": "https://facebook.com/updated-ai-academy",
    "twitter": "https://twitter.com/updated-ai-academy",
    "linkedin": "https://linkedin.com/company/updated-ai-academy"
}

response = requests.put(
    f"{API_URL}/admin/social-links", 
    json=social_data, 
    headers=headers
)
print_response(response)

if response.status_code == 200:
    test_results["social_links"]["update"] = True
    print_test_result("Update Social Links", True)
else:
    print_test_result("Update Social Links", False)

# Phase 4: Role-Based Access Control
print_section("Phase 4: Role-Based Access Control")

# First, login as instructor
print("\n12. Login as Instructor")
if "instructor_email" in locals() and "instructor_password" in locals():
    # Login as instructor
    login_data = {
        "email": instructor_email,
        "password": instructor_password
    }
    
    response = requests.post(f"{API_URL}/auth/login", json=login_data)
    print_response(response)
    
    if response.status_code == 200:
        tokens["instructor"] = response.json().get("access_token")
        print_test_result("Instructor Login", True)
        
        # Test 13: Try to access admin endpoint as instructor (should fail)
        print("\n13. Testing Non-Admin Access Denied")
        instructor_headers = {"Authorization": f"Bearer {tokens['instructor']}"}
        
        response = requests.get(f"{API_URL}/admin/users", headers=instructor_headers)
        print_response(response)
        
        if response.status_code == 403:
            test_results["role_based_access"]["non_admin_access_denied"] = True
            print_test_result("Non-Admin Access Denied", True)
        else:
            print_test_result("Non-Admin Access Denied", False)
    else:
        print_test_result("Instructor Login", False)
else:
    print("Skipping instructor login test - no instructor credentials available")

# Test 14: Verify admin-only endpoints
print("\n14. Testing Admin-Only Endpoints")
admin_endpoints = [
    {"method": "get", "url": f"{API_URL}/admin/users"},
    {"method": "get", "url": f"{API_URL}/admin/contact-info"},
    {"method": "get", "url": f"{API_URL}/admin/social-links"}
]

admin_only_success = True
for endpoint in admin_endpoints:
    method = endpoint["method"]
    url = endpoint["url"]
    
    if method == "get":
        response = requests.get(url, headers=headers)
    
    print(f"Testing {method.upper()} {url}: {response.status_code}")
    
    if response.status_code != 200:
        admin_only_success = False
        print(f"Failed on {method.upper()} {url}")

test_results["role_based_access"]["admin_only_endpoints"] = admin_only_success
print_test_result("Admin-Only Endpoints", admin_only_success)

# Print summary
print_section("Test Summary")
for phase, result in test_results.items():
    if isinstance(result, dict):
        print(f"\n{phase.replace('_', ' ').title()}:")
        for test, passed in result.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {status} - {test.replace('_', ' ').title()}")
    else:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {phase.replace('_', ' ').title()}")

# Overall result
all_passed = all([
    test_results["super_admin_creation"],
    test_results["admin_login"],
    all(test_results["user_management"].values()),
    all(test_results["contact_info"].values()),
    all(test_results["social_links"].values()),
    all(test_results["role_based_access"].values())
])

print("\nOverall Test Result:", "✅ PASSED" if all_passed else "❌ FAILED")