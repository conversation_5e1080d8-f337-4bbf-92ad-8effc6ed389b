import requests
import json
import time
import os
import sys
import random
import string
from pprint import pprint

# Get the backend URL from environment or use a default
BACKEND_URL = "https://10fab1d6-02b0-4d8b-826a-6ef585309ae8.preview.emergentagent.com"
API_URL = f"{BACKEND_URL}/api"

class SuperAdminTester:
    def __init__(self):
        self.token = None
        self.user_data = None
        self.tests_run = 0
        self.tests_passed = 0
        self.created_user_ids = []
        self.super_admin_email = "<EMAIL>"
        self.super_admin_password = "adminpassword123"

    def run_test(self, name, method, endpoint, expected_status, data=None, auth=True):
        """Run a single API test"""
        url = f"{API_URL}/{endpoint}"
        headers = {'Content-Type': 'application/json'}
        if auth and self.token:
            headers['Authorization'] = f'Bearer {self.token}'

        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=headers)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers)

            success = response.status_code == expected_status
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    return success, response.json()
                except:
                    return success, {}
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                try:
                    print(f"Response: {response.json()}")
                except:
                    print(f"Response: {response.text}")
                return False, {}

        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def test_create_super_admin(self):
        """Test creating a super admin"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        data = {
            "email": f"superadmin_{random_suffix}@test.com",
            "password": "superadmin123",
            "name": f"Super Admin {random_suffix}"
        }
        
        success, response = self.run_test(
            "Create Super Admin",
            "POST",
            "auth/create-super-admin",
            200,
            data=data,
            auth=False
        )
        
        if success:
            print(f"Created super admin: {data['email']}")
            # Store credentials for login
            self.super_admin_email = data['email']
            self.super_admin_password = "superadmin123"
            return True
        else:
            # If we get a 400 error with "Super admin already exists", that's expected
            if isinstance(response, dict) and "detail" in response and "Super admin already exists" in response["detail"]:
                print("Super admin already exists, will try to login with default credentials")
                # Try different common credentials
                self.super_admin_credentials = [
                    {"email": "<EMAIL>", "password": "adminpassword123"},
                    {"email": "<EMAIL>", "password": "superadmin123"},
                    {"email": "<EMAIL>", "password": "admin123"}
                ]
                return True
            return False

    def test_duplicate_super_admin(self):
        """Test that only one super admin can be created"""
        data = {
            "email": "<EMAIL>",
            "password": "admin456",
            "name": "Another Admin"
        }
        
        success, response = self.run_test(
            "Create Duplicate Super Admin (should fail)",
            "POST",
            "auth/create-super-admin",
            400,
            data=data,
            auth=False
        )
        
        # For this test, success means the API correctly rejected the request
        return success

    def test_admin_login(self):
        """Test login with super admin credentials"""
        if hasattr(self, 'super_admin_credentials'):
            # Try different credentials
            for creds in self.super_admin_credentials:
                print(f"Trying to login with {creds['email']}")
                data = {
                    "email": creds['email'],
                    "password": creds['password']
                }
                
                success, response = self.run_test(
                    f"Super Admin Login ({creds['email']})",
                    "POST",
                    "auth/login",
                    200,
                    data=data,
                    auth=False
                )
                
                if success:
                    self.token = response.get('access_token')
                    self.user_data = response.get('user')
                    print(f"Logged in as super admin: {creds['email']}")
                    
                    # Verify the user has admin role
                    if self.user_data.get('role') == 'admin':
                        print("✅ User has admin role")
                        return True
                    else:
                        print(f"❌ User does not have admin role: {self.user_data.get('role')}")
                        # Continue trying other credentials
            
            # If we get here, none of the credentials worked
            return False
        else:
            # Use the credentials from the created super admin
            data = {
                "email": self.super_admin_email,
                "password": self.super_admin_password
            }
            
            success, response = self.run_test(
                "Super Admin Login",
                "POST",
                "auth/login",
                200,
                data=data,
                auth=False
            )
            
            if success:
                self.token = response.get('access_token')
                self.user_data = response.get('user')
                print(f"Logged in as super admin: {self.super_admin_email}")
                
                # Verify the user has admin role
                if self.user_data.get('role') == 'admin':
                    print("✅ User has admin role")
                    return True
                else:
                    print(f"❌ User does not have admin role: {self.user_data.get('role')}")
                    return False
            
            return False

    def test_get_all_users(self):
        """Test getting all users (admin only)"""
        success, response = self.run_test(
            "Get All Users",
            "GET",
            "admin/users",
            200
        )
        return success

    def test_create_user(self, role):
        """Test creating a new user"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        data = {
            "email": f"{role}_{random_suffix}@test.com",
            "password": f"{role}123",
            "name": f"Test {role.capitalize()} {random_suffix}",
            "role": role
        }
        
        success, response = self.run_test(
            f"Create {role.capitalize()} User",
            "POST",
            "admin/users",
            200,
            data=data
        )
        
        if success and response.get('id'):
            self.created_user_ids.append(response.get('id'))
            print(f"Created {role} with ID: {response.get('id')}")
            
            # Store credentials for the instructor to test role-based access
            if role == 'instructor':
                self.instructor_email = data['email']
                self.instructor_password = data['password']
        
        return success

    def test_update_user(self):
        """Test updating a user"""
        if not self.created_user_ids:
            print("❌ Skipping update user - No user IDs available")
            return False
            
        user_id = self.created_user_ids[0]
        data = {
            "name": "Updated User Name",
            "bio": "This is an updated user bio"
        }
        
        success, response = self.run_test(
            "Update User",
            "PUT",
            f"admin/users/{user_id}",
            200,
            data=data
        )
        return success

    def test_delete_user(self):
        """Test deleting a user"""
        if len(self.created_user_ids) < 2:
            print("❌ Skipping delete user - Not enough user IDs available")
            return False
            
        user_id = self.created_user_ids[1]
        
        success, response = self.run_test(
            "Delete User",
            "DELETE",
            f"admin/users/{user_id}",
            200
        )
        
        if success:
            self.created_user_ids.remove(user_id)
        
        return success

    def test_get_contact_info(self):
        """Test getting contact information"""
        success, response = self.run_test(
            "Get Contact Info (Public)",
            "GET",
            "contact-info",
            200,
            auth=False
        )
        return success

    def test_update_contact_info(self):
        """Test updating contact information (admin only)"""
        data = {
            "company_name": "Updated AI Academy",
            "phone": "+****************",
            "email": "<EMAIL>"
        }
        
        success, response = self.run_test(
            "Update Contact Info",
            "PUT",
            "admin/contact-info",
            200,
            data=data
        )
        return success

    def test_get_social_links(self):
        """Test getting social media links"""
        success, response = self.run_test(
            "Get Social Links (Public)",
            "GET",
            "social-links",
            200,
            auth=False
        )
        return success

    def test_update_social_links(self):
        """Test updating social media links (admin only)"""
        data = {
            "facebook": "https://facebook.com/updated-ai-academy",
            "twitter": "https://twitter.com/updated-ai-academy",
            "linkedin": "https://linkedin.com/company/updated-ai-academy"
        }
        
        success, response = self.run_test(
            "Update Social Links",
            "PUT",
            "admin/social-links",
            200,
            data=data
        )
        return success

    def test_instructor_login(self):
        """Test login with instructor credentials"""
        if not hasattr(self, 'instructor_email'):
            print("❌ Skipping instructor login - No instructor credentials available")
            return False
            
        data = {
            "email": self.instructor_email,
            "password": f"instructor123"
        }
        
        success, response = self.run_test(
            "Instructor Login",
            "POST",
            "auth/login",
            200,
            data=data,
            auth=False
        )
        
        if success:
            # Save the admin token temporarily
            admin_token = self.token
            
            # Set the instructor token
            self.token = response.get('access_token')
            self.user_data = response.get('user')
            print(f"Logged in as instructor: {self.instructor_email}")
            
            # Test accessing admin endpoint (should fail)
            admin_access, _ = self.run_test(
                "Access Admin Endpoint as Instructor (should fail)",
                "GET",
                "admin/users",
                403
            )
            
            # Restore admin token
            self.token = admin_token
            
            return admin_access
        
        return False

def run_super_admin_tests():
    print("\n🧪 TESTING SUPER ADMIN FUNCTIONALITY 🧪")
    tester = SuperAdminTester()
    
    # Phase 1: Authentication & Super Admin Setup
    print("\n=== Phase 1: Authentication & Super Admin Setup ===")
    
    # Try to create super admin (may fail if one already exists)
    tester.test_create_super_admin()
    
    # Test that only one super admin can be created
    tester.test_duplicate_super_admin()
    
    # Login as super admin
    if not tester.test_admin_login():
        print("❌ Failed to login as super admin")
        return False
    
    # Phase 2: User Management (Admin Only)
    print("\n=== Phase 2: User Management (Admin Only) ===")
    
    # Test listing all users
    tester.test_get_all_users()
    
    # Test creating users with different roles
    tester.test_create_user("student")
    tester.test_create_user("instructor")
    tester.test_create_user("admin")
    
    # Test updating a user
    tester.test_update_user()
    
    # Test deleting a user
    tester.test_delete_user()
    
    # Phase 3: Contact & Social Links Management
    print("\n=== Phase 3: Contact & Social Links Management ===")
    
    # Test getting and updating contact info
    tester.test_get_contact_info()
    tester.test_update_contact_info()
    
    # Test getting and updating social links
    tester.test_get_social_links()
    tester.test_update_social_links()
    
    # Phase 4: Role-Based Access Control
    print("\n=== Phase 4: Role-Based Access Control ===")
    
    # Test that non-admin users cannot access admin endpoints
    tester.test_instructor_login()
    
    # Print summary
    print("\n📊 SUPER ADMIN TEST SUMMARY")
    print(f"Tests passed: {tester.tests_passed}/{tester.tests_run}")
    
    return tester.tests_passed == tester.tests_run

if __name__ == "__main__":
    print("🚀 Starting Super Admin Functionality Tests")
    
    success = run_super_admin_tests()
    
    print(f"\n🏁 OVERALL RESULT: {'✅ PASSED' if success else '❌ FAILED'}")
    
    sys.exit(0 if success else 1)