import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { fetchDashboardStats, fetchMyCourses } from '../api';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  // Professional AI course images
  const courseImages = [
    'https://images.unsplash.com/photo-1516110833967-0b5716ca1387',
    'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg',
    'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg',
    'https://images.unsplash.com/photo-1498050108023-c5249f4df085',
    'https://images.unsplash.com/photo-1591696331111-ef9586a5b17a'
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, coursesResponse] = await Promise.all([
        fetchDashboardStats(),
        fetchMyCourses()
      ]);
      
      setStats(statsResponse.data);
      setCourses(coursesResponse.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCourseImage = (index) => {
    return courseImages[index % courseImages.length];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const isNewUser = user?.role === 'instructor' && courses.length === 0;
  const isNewStudent = user?.role === 'student' && courses.length === 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}! 👋
          </h1>
          <p className="text-gray-600 mt-2">
            {user?.role === 'instructor' 
              ? 'Ready to inspire and teach? Manage your courses and track student progress' 
              : 'Continue your AI learning journey and unlock new possibilities'
            }
          </p>
        </div>

        {/* New User Welcome Section */}
        {(isNewUser || isNewStudent) && (
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">
                  {isNewUser ? '🚀 Ready to create your first course?' : '🎯 Ready to start learning?'}
                </h2>
                <p className="text-blue-100 mb-4">
                  {isNewUser 
                    ? 'Share your AI expertise with students around the world. Create engaging courses with videos, text content, and quizzes.'
                    : 'Explore our AI courses and start building your skills today. Learn from industry experts and advance your career.'
                  }
                </p>
                <a
                  href={isNewUser ? '/create-course' : '/'}
                  className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200"
                >
                  {isNewUser ? 'Create Your First Course' : 'Browse Courses'}
                </a>
              </div>
              <div className="hidden md:block">
                <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-4xl">
                    {isNewUser ? '📚' : '🎓'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {user?.role === 'instructor' ? (
              <>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-100 mr-4">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Total Courses</h3>
                      <p className="text-3xl font-bold text-blue-600 mt-1">{stats.total_courses}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-green-100 mr-4">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Total Students</h3>
                      <p className="text-3xl font-bold text-green-600 mt-1">{stats.total_students}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-purple-100 mr-4">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Published Courses</h3>
                      <p className="text-3xl font-bold text-purple-600 mt-1">
                        {courses.filter(c => c.is_published).length}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-100 mr-4">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Enrolled Courses</h3>
                      <p className="text-3xl font-bold text-blue-600 mt-1">{stats.total_enrolled}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-green-100 mr-4">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Completed Courses</h3>
                      <p className="text-3xl font-bold text-green-600 mt-1">{stats.completed_courses}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition duration-200">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-yellow-100 mr-4">
                      <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">In Progress</h3>
                      <p className="text-3xl font-bold text-yellow-600 mt-1">
                        {stats.total_enrolled - stats.completed_courses}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Courses Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              {user?.role === 'instructor' ? 'My Courses' : 'My Learning'}
            </h2>
            {user?.role === 'instructor' && (
              <a
                href="/create-course"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
              >
                ✨ Create Course
              </a>
            )}
          </div>
          
          <div className="p-6">
            {courses.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">
                    {user?.role === 'instructor' ? '📚' : '🎓'}
                  </span>
                </div>
                <p className="text-gray-500 text-lg mb-4">
                  {user?.role === 'instructor' 
                    ? 'You haven\'t created any courses yet.' 
                    : 'You haven\'t enrolled in any courses yet.'
                  }
                </p>
                <p className="text-gray-400 mb-6">
                  {user?.role === 'instructor' 
                    ? 'Start sharing your knowledge and expertise with students worldwide.'
                    : 'Discover amazing AI courses and start your learning journey today.'
                  }
                </p>
                {user?.role === 'instructor' ? (
                  <a
                    href="/create-course"
                    className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Your First Course
                  </a>
                ) : (
                  <a
                    href="/"
                    className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Browse Courses
                  </a>
                )}
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {courses.map((course, index) => (
                  <div key={course.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition duration-300">
                    <div className="relative">
                      <img 
                        src={course.cover_image || getCourseImage(index)} 
                        alt={course.title} 
                        className="w-full h-32 object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-semibold ${
                          course.is_published 
                            ? 'bg-green-500 text-white' 
                            : 'bg-yellow-500 text-white'
                        }`}>
                          {course.is_published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <h3 className="text-lg font-bold mb-2 text-gray-900">{course.title}</h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{course.description}</p>
                      
                      <div className="flex items-center justify-between mb-3 text-sm text-gray-500">
                        <span>{course.student_count} students</span>
                        {course.duration_minutes && (
                          <span>{Math.floor(course.duration_minutes / 60)}h {course.duration_minutes % 60}m</span>
                        )}
                      </div>
                      
                      <a
                        href={`/course/${course.id}`}
                        className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition duration-200 transform hover:scale-105"
                      >
                        {user?.role === 'instructor' ? 'Manage Course' : 'Continue Learning'}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
