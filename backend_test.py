
import requests
import sys
import random
import string
import time
from datetime import datetime

class LMSAPITester:
    def __init__(self, base_url="https://10fab1d6-02b0-4d8b-826a-6ef585309ae8.preview.emergentagent.com"):
        self.base_url = base_url
        self.token = None
        self.user_data = None
        self.tests_run = 0
        self.tests_passed = 0
        self.course_id = None
        self.lesson_id = None
        self.discussion_id = None
        self.review_id = None

    def run_test(self, name, method, endpoint, expected_status, data=None, auth=True):
        """Run a single API test"""
        url = f"{self.base_url}/api/{endpoint}"
        headers = {'Content-Type': 'application/json'}
        if auth and self.token:
            headers['Authorization'] = f'Bearer {self.token}'

        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=headers)

            success = response.status_code == expected_status
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    return success, response.json()
                except:
                    return success, {}
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                try:
                    print(f"Response: {response.json()}")
                except:
                    print(f"Response: {response.text}")
                return False, {}

        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def test_register_student(self):
        """Test student registration"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        email = f"student_{random_suffix}@test.com"
        data = {
            "email": email,
            "password": "password123",
            "name": f"Test Student {random_suffix}",
            "role": "student"
        }
        
        success, response = self.run_test(
            "Student Registration",
            "POST",
            "auth/register",
            200,
            data=data,
            auth=False
        )
        
        if success:
            self.token = response.get('access_token')
            self.user_data = response.get('user')
            print(f"Registered student: {email}")
        
        return success

    def test_register_instructor(self):
        """Test instructor registration"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        email = f"instructor_{random_suffix}@test.com"
        data = {
            "email": email,
            "password": "password123",
            "name": f"Test Instructor {random_suffix}",
            "role": "instructor"
        }
        
        success, response = self.run_test(
            "Instructor Registration",
            "POST",
            "auth/register",
            200,
            data=data,
            auth=False
        )
        
        if success:
            self.token = response.get('access_token')
            self.user_data = response.get('user')
            print(f"Registered instructor: {email}")
        
        return success

    def test_login(self, email, password):
        """Test login functionality"""
        data = {
            "email": email,
            "password": password
        }
        
        success, response = self.run_test(
            "Login",
            "POST",
            "auth/login",
            200,
            data=data,
            auth=False
        )
        
        if success:
            self.token = response.get('access_token')
            self.user_data = response.get('user')
            print(f"Logged in as: {email}")
        
        return success

    def test_get_me(self):
        """Test get current user endpoint"""
        success, response = self.run_test(
            "Get Current User",
            "GET",
            "auth/me",
            200
        )
        return success

    def test_create_course(self):
        """Test course creation"""
        if not self.token or self.user_data.get('role') != 'instructor':
            print("❌ Skipping course creation - Not logged in as instructor")
            return False
            
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
        data = {
            "title": f"Test Course {random_suffix}",
            "description": "This is a test course created by the API tester",
            "price": 0.0,
            "category": "AI & Technology",
            "duration_minutes": 60
        }
        
        success, response = self.run_test(
            "Create Course",
            "POST",
            "courses",
            200,
            data=data
        )
        
        if success:
            self.course_id = response.get('id')
            print(f"Created course with ID: {self.course_id}")
        
        return success

    def test_get_courses(self):
        """Test getting all courses"""
        success, response = self.run_test(
            "Get All Courses",
            "GET",
            "courses",
            200,
            auth=False
        )
        return success

    def test_get_course(self):
        """Test getting a specific course"""
        if not self.course_id:
            print("❌ Skipping get course - No course ID available")
            return False
            
        success, response = self.run_test(
            "Get Course",
            "GET",
            f"courses/{self.course_id}",
            200,
            auth=False
        )
        return success

    def test_publish_course(self):
        """Test publishing a course"""
        if not self.course_id:
            print("❌ Skipping publish course - No course ID available")
            return False
            
        success, response = self.run_test(
            "Publish Course",
            "PUT",
            f"courses/{self.course_id}/publish",
            200
        )
        return success

    def test_create_lesson(self):
        """Test creating a lesson"""
        if not self.course_id:
            print("❌ Skipping create lesson - No course ID available")
            return False
            
        data = {
            "title": "Introduction to the Course",
            "description": "This is the first lesson of the course",
            "type": "text",
            "content": "<p>Welcome to the course! This is the content of the first lesson.</p>",
            "order": 1,
            "duration_minutes": 15
        }
        
        success, response = self.run_test(
            "Create Lesson",
            "POST",
            f"courses/{self.course_id}/lessons",
            200,
            data=data
        )
        
        if success:
            self.lesson_id = response.get('id')
            print(f"Created lesson with ID: {self.lesson_id}")
        
        return success

    def test_get_lessons(self):
        """Test getting all lessons for a course"""
        if not self.course_id:
            print("❌ Skipping get lessons - No course ID available")
            return False
            
        success, response = self.run_test(
            "Get Course Lessons",
            "GET",
            f"courses/{self.course_id}/lessons",
            200
        )
        return success

    def test_enroll_in_course(self):
        """Test enrolling in a course"""
        if not self.course_id or self.user_data.get('role') != 'student':
            print("❌ Skipping enrollment - Not logged in as student or no course ID")
            return False
            
        success, response = self.run_test(
            "Enroll in Course",
            "POST",
            f"courses/{self.course_id}/enroll",
            200
        )
        return success

    def test_get_my_courses(self):
        """Test getting user's courses"""
        success, response = self.run_test(
            "Get My Courses",
            "GET",
            "my-courses",
            200
        )
        return success

    def test_get_dashboard_stats(self):
        """Test getting dashboard stats"""
        success, response = self.run_test(
            "Get Dashboard Stats",
            "GET",
            "dashboard/stats",
            200
        )
        return success

    def test_complete_lesson(self):
        """Test marking a lesson as complete"""
        if not self.lesson_id or self.user_data.get('role') != 'student':
            print("❌ Skipping complete lesson - Not logged in as student or no lesson ID")
            return False
            
        success, response = self.run_test(
            "Complete Lesson",
            "POST",
            f"lessons/{self.lesson_id}/complete",
            200
        )
        return success

    def test_get_course_progress(self):
        """Test getting course progress"""
        if not self.course_id or self.user_data.get('role') != 'student':
            print("❌ Skipping get progress - Not logged in as student or no course ID")
            return False
            
        success, response = self.run_test(
            "Get Course Progress",
            "GET",
            f"courses/{self.course_id}/progress",
            200
        )
        return success
    
    # New methods for testing the enhanced features
    
    def test_create_discussion(self):
        """Test creating a discussion post"""
        if not self.course_id:
            print("❌ Skipping create discussion - No course ID available")
            return False
            
        data = {
            "message": "This is a test discussion post created by the API tester"
        }
        
        success, response = self.run_test(
            "Create Discussion",
            "POST",
            f"courses/{self.course_id}/discussions",
            200,
            data=data
        )
        
        if success:
            self.discussion_id = response.get('id')
            print(f"Created discussion with ID: {self.discussion_id}")
        
        return success
    
    def test_get_discussions(self):
        """Test getting all discussions for a course"""
        if not self.course_id:
            print("❌ Skipping get discussions - No course ID available")
            return False
            
        success, response = self.run_test(
            "Get Course Discussions",
            "GET",
            f"courses/{self.course_id}/discussions",
            200
        )
        return success
    
    def test_create_review(self):
        """Test creating a course review"""
        if not self.course_id or self.user_data.get('role') != 'student':
            print("❌ Skipping create review - Not logged in as student or no course ID")
            return False
            
        data = {
            "rating": 5,
            "review": "This is a test review created by the API tester. Great course!"
        }
        
        success, response = self.run_test(
            "Create Review",
            "POST",
            f"courses/{self.course_id}/reviews",
            200,
            data=data
        )
        
        if success:
            self.review_id = response.get('id')
            print(f"Created review with ID: {self.review_id}")
        
        return success
    
    def test_get_reviews(self):
        """Test getting all reviews for a course"""
        if not self.course_id:
            print("❌ Skipping get reviews - No course ID available")
            return False
            
        success, response = self.run_test(
            "Get Course Reviews",
            "GET",
            f"courses/{self.course_id}/reviews",
            200,
            auth=False
        )
        return success
    
    def test_newsletter_subscribe(self):
        """Test newsletter subscription"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        email = f"subscriber_{random_suffix}@test.com"
        data = {
            "email": email,
            "name": f"Test Subscriber {random_suffix}"
        }
        
        success, response = self.run_test(
            "Newsletter Subscribe",
            "POST",
            "newsletter/subscribe",
            200,
            data=data,
            auth=False
        )
        return success
    
    def test_get_newsletter_subscribers(self):
        """Test getting newsletter subscribers"""
        if not self.token or self.user_data.get('role') != 'instructor':
            print("❌ Skipping get subscribers - Not logged in as instructor")
            return False
            
        success, response = self.run_test(
            "Get Newsletter Subscribers",
            "GET",
            "newsletter/subscribers",
            200
        )
        return success

def test_instructor_flow():
    print("\n🧪 TESTING INSTRUCTOR FLOW 🧪")
    tester = LMSAPITester()
    
    # Test with existing instructor account
    if not tester.test_login("<EMAIL>", "password123"):
        # Try registering a new instructor if login fails
        if not tester.test_register_instructor():
            print("❌ Failed to register or login as instructor")
            return False
    
    tester.test_get_me()
    tester.test_create_course()
    tester.test_get_course()
    tester.test_create_lesson()
    tester.test_get_lessons()
    tester.test_publish_course()
    tester.test_get_my_courses()
    
    # Test new features
    tester.test_create_discussion()
    tester.test_get_discussions()
    tester.test_get_newsletter_subscribers()
    
    tester.test_get_dashboard_stats()
    
    print(f"\n📊 Instructor Tests passed: {tester.tests_passed}/{tester.tests_run}")
    return tester.tests_passed == tester.tests_run

def test_student_flow():
    print("\n🧪 TESTING STUDENT FLOW 🧪")
    tester = LMSAPITester()
    
    # Test with existing student account
    if not tester.test_login("<EMAIL>", "password123"):
        # Try registering a new student if login fails
        if not tester.test_register_student():
            print("❌ Failed to register or login as student")
            return False
    
    tester.test_get_me()
    tester.test_get_courses()
    
    # Find a course to enroll in
    success, courses = tester.run_test(
        "Get Available Courses",
        "GET",
        "courses",
        200,
        auth=False
    )
    
    if success and courses:
        # Try to find the "AI Fundamentals for Beginners" course
        target_course = next((c for c in courses if c.get('title') == "AI Fundamentals for Beginners"), None)
        
        # If not found, use the first available course
        if not target_course and courses:
            target_course = courses[0]
            
        if target_course:
            tester.course_id = target_course.get('id')
            print(f"Found course to enroll in: {target_course.get('title')} (ID: {tester.course_id})")
            
            tester.test_enroll_in_course()
            tester.test_get_my_courses()
            
            # Get lessons for the course
            success, lessons = tester.run_test(
                "Get Course Lessons",
                "GET",
                f"courses/{tester.course_id}/lessons",
                200
            )
            
            if success and lessons:
                tester.lesson_id = lessons[0].get('id')
                tester.test_complete_lesson()
                tester.test_get_course_progress()
                
                # Test new features
                tester.test_create_discussion()
                tester.test_get_discussions()
                tester.test_create_review()
                tester.test_get_reviews()
    
    tester.test_get_dashboard_stats()
    
    print(f"\n📊 Student Tests passed: {tester.tests_passed}/{tester.tests_run}")
    return tester.tests_passed == tester.tests_run

def test_anonymous_flow():
    print("\n🧪 TESTING ANONYMOUS USER FLOW 🧪")
    tester = LMSAPITester()
    
    tester.test_get_courses()
    
    # Test newsletter subscription
    tester.test_newsletter_subscribe()
    
    # Find a course to view reviews
    success, courses = tester.run_test(
        "Get Available Courses",
        "GET",
        "courses",
        200,
        auth=False
    )
    
    if success and courses and len(courses) > 0:
        tester.course_id = courses[0].get('id')
        tester.test_get_reviews()
    
    # Try to access protected endpoints (should fail)
    success, _ = tester.run_test(
        "Access Protected Endpoint (should fail)",
        "GET",
        "my-courses",
        401,
        auth=False
    )
    
    print(f"\n📊 Anonymous Tests passed: {tester.tests_passed}/{tester.tests_run}")
    return tester.tests_passed == tester.tests_run

def test_contact_endpoints():
    print("\n🧪 TESTING CONTACT ENDPOINTS 🧪")
    tester = LMSAPITester()
    
    # Test contact form submission
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    contact_data = {
        "name": f"Test Contact {random_suffix}",
        "email": f"contact_{random_suffix}@test.com",
        "phone": "************",
        "subject": "Test Contact Form",
        "message": "This is a test message from the automated test suite."
    }
    
    success, response = tester.run_test(
        "Submit Contact Form",
        "POST",
        "contact/submit",
        200,
        data=contact_data,
        auth=False
    )
    
    # Store the submission ID for future tests
    if success:
        submission_id = response.get('id')
        print(f"Created contact submission with ID: {submission_id}")
    
    # Test getting contact info
    success, contact_info = tester.run_test(
        "Get Contact Info",
        "GET",
        "contact-info",
        200,
        auth=False
    )
    
    # Test getting social links
    success, social_links = tester.run_test(
        "Get Social Links",
        "GET",
        "social-links",
        200,
        auth=False
    )
    
    # Test admin endpoints (requires instructor login)
    if not tester.test_login("<EMAIL>", "password123"):
        # Try registering a new instructor if login fails
        if not tester.test_register_instructor():
            print("❌ Failed to register or login as instructor")
            return False
    
    # Test getting contact submissions
    success, submissions = tester.run_test(
        "Get Contact Submissions",
        "GET",
        "admin/contact-submissions",
        200
    )
    
    # Test updating contact info
    updated_contact_info = {
        "company_name": "Updated AI Academy",
        "address": "456 Tech Boulevard",
        "city": "New York",
        "state": "NY",
        "zip_code": "10001",
        "country": "USA",
        "phone": "+****************",
        "email": "<EMAIL>",
        "business_hours": "Monday - Friday: 8:00 AM - 5:00 PM EST"
    }
    
    success, _ = tester.run_test(
        "Update Contact Info",
        "PUT",
        "admin/contact-info",
        200,
        data=updated_contact_info
    )
    
    # Verify the update
    success, updated_info = tester.run_test(
        "Verify Updated Contact Info",
        "GET",
        "contact-info",
        200,
        auth=False
    )
    
    # Test updating social links
    updated_social_links = {
        "facebook": "https://facebook.com/updated-ai-academy",
        "youtube": "https://youtube.com/updated-ai-academy",
        "linkedin": "https://linkedin.com/company/updated-ai-academy",
        "twitter": "https://twitter.com/updated_ai_academy",
        "instagram": "https://instagram.com/updated_ai_academy"
    }
    
    success, _ = tester.run_test(
        "Update Social Links",
        "PUT",
        "admin/social-links",
        200,
        data=updated_social_links
    )
    
    # Verify the update
    success, updated_links = tester.run_test(
        "Verify Updated Social Links",
        "GET",
        "social-links",
        200,
        auth=False
    )
    
    # If we have a submission ID, test marking it as read
    if 'submission_id' in locals():
        success, _ = tester.run_test(
            "Mark Contact Submission as Read",
            "PUT",
            f"admin/contact-submissions/{submission_id}/mark-read",
            200
        )
    
    print(f"\n📊 Contact Endpoints Tests passed: {tester.tests_passed}/{tester.tests_run}")
    return tester.tests_passed == tester.tests_run

def test_instructor_profiles():
    print("\n🧪 TESTING INSTRUCTOR PROFILES 🧪")
    tester = LMSAPITester()
    
    # Login as admin/instructor
    if not tester.test_login("<EMAIL>", "password123"):
        # Try registering a new instructor if login fails
        if not tester.test_register_instructor():
            print("❌ Failed to register or login as instructor")
            return False
    
    # Test 1: Instructor Profiles API
    # Get all instructor profiles
    success, profiles = tester.run_test(
        "Get Instructor Profiles",
        "GET",
        "admin/instructor-profiles",
        200
    )
    
    # Initialize default profiles if none exist
    if success and (not profiles or len(profiles) == 0):
        success, _ = tester.run_test(
            "Initialize Default Instructor Profiles",
            "POST",
            "admin/instructor-profiles/init-default",
            200
        )
        
        if success:
            # Get profiles again after initialization
            success, profiles = tester.run_test(
                "Get Instructor Profiles After Init",
                "GET",
                "admin/instructor-profiles",
                200
            )
    
    # Test 2: Instructor Profile Creation with Expertise
    # Create a new instructor profile with expertise as an array
    test_profile = {
        "name": "Test Instructor",
        "title": "AI Testing Expert",
        "avatar": "https://example.com/avatar.jpg",
        "bio": "Expert in testing AI applications",
        "expertise": ["Testing", "AI", "Python"],
        "experience": "5+ years",
        "students": "1,000+",
        "rating": 4.8,
        "courses": 3,
        "company": "Test Company",
        "education": "PhD in Computer Science"
    }
    
    success, created_profile = tester.run_test(
        "Create Instructor Profile",
        "POST",
        "admin/instructor-profiles",
        200,
        data=test_profile
    )
    
    profile_id = None
    if success:
        profile_id = created_profile.get('id')
        
        # Verify expertise is an array
        if isinstance(created_profile.get('expertise'), list):
            print("✅ Expertise is correctly stored as an array")
        else:
            print("❌ Expertise is not stored as an array")
            success = False
    
    # Test 3: Update Instructor Profile
    if profile_id:
        # Update the profile with new expertise
        update_data = {
            "expertise": ["Python", "TensorFlow", "PyTorch", "Computer Vision"]
        }
        
        success, updated_profile = tester.run_test(
            "Update Instructor Profile",
            "PUT",
            f"admin/instructor-profiles/{profile_id}",
            200,
            data=update_data
        )
        
        if success:
            # Verify expertise is still an array after update
            if isinstance(updated_profile.get('expertise'), list):
                print("✅ Updated expertise is correctly stored as an array")
            else:
                print("❌ Updated expertise is not stored as an array")
                success = False
    
    # Test 4: Get Public Instructor Profiles
    success, public_profiles = tester.run_test(
        "Get Public Instructor Profiles",
        "GET",
        "instructor-profiles",
        200,
        auth=False
    )
    
    if success:
        # Verify all profiles have expertise as arrays
        all_valid = True
        for profile in public_profiles:
            if not isinstance(profile.get('expertise'), list):
                all_valid = False
                print(f"❌ Profile {profile.get('id')} has expertise not stored as an array")
        
        if all_valid:
            print("✅ All public profiles have expertise correctly stored as arrays")
        else:
            success = False
    
    print(f"\n📊 Instructor Profiles Tests passed: {tester.tests_passed}/{tester.tests_run}")
    return tester.tests_passed == tester.tests_run

if __name__ == "__main__":
    print("🚀 Starting LMS API Tests")
    
    anonymous_success = test_anonymous_flow()
    instructor_success = test_instructor_flow()
    student_success = test_student_flow()
    contact_success = test_contact_endpoints()
    instructor_profiles_success = test_instructor_profiles()
    
    print("\n📋 TEST SUMMARY")
    print(f"Anonymous Flow: {'✅ PASSED' if anonymous_success else '❌ FAILED'}")
    print(f"Instructor Flow: {'✅ PASSED' if instructor_success else '❌ FAILED'}")
    print(f"Student Flow: {'✅ PASSED' if student_success else '❌ FAILED'}")
    print(f"Contact Endpoints: {'✅ PASSED' if contact_success else '❌ FAILED'}")
    print(f"Instructor Profiles: {'✅ PASSED' if instructor_profiles_success else '❌ FAILED'}")
    
    overall_success = anonymous_success and instructor_success and student_success and contact_success and instructor_profiles_success
    print(f"\n🏁 OVERALL RESULT: {'✅ PASSED' if overall_success else '❌ FAILED'}")
    
    sys.exit(0 if overall_success else 1)
