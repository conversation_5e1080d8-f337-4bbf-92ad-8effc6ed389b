#!/usr/bin/env python3
"""
Simple MongoDB Connection Test
"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv

load_dotenv()

def test_simple_connection():
    """Test MongoDB connection with pymongo directly"""
    
    mongo_url = os.getenv("MONGO_URL")
    print(f"Testing connection to: {mongo_url[:50]}...")
    
    try:
        # Create client with short timeout
        client = MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        
        # Test connection
        print("Testing server info...")
        server_info = client.server_info()
        print(f"✅ Connected! MongoDB version: {server_info['version']}")
        
        # Test database access
        db = client.lmsdb
        collections = db.list_collection_names()
        print(f"✅ Database access successful! Collections: {len(collections)}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    test_simple_connection()
