import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import CourseDetailsPage from './CourseDetailsPage';
import CreateCoursePage from './CreateCoursePage';
import { AuthProvider } from './hooks/useAuth';
import NewsletterSignup from './components/NewsletterSignup';
import Navigation from './components/Navigation';
import HomePage from './components/HomePage';
import LoginPage from './components/LoginPage';
import RegisterPage from './components/RegisterPage';
import Dashboard from './components/Dashboard';
import ContactPage from './components/ContactPage';
import CoursePage from './components/CoursePage';
import ProtectedRoute from './components/ProtectedRoute';
import InstructorsPage from './components/InstructorsPage';
import CMSAdminPage from './components/CMSAdminPage';

// Main App Component
function App() {
  return (
    <AuthProvider>
      <Router>
        <>
          <Navigation />
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/instructors" 
              element={<InstructorsPage />} 
            />
            <Route 
              path="/admin" 
              element={
                <ProtectedRoute>
                  <CMSAdminPage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/create-course" 
              element={
                <ProtectedRoute>
                  <CreateCoursePage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/course/:id" 
              element={
                <ProtectedRoute>
                  <CoursePage />
                </ProtectedRoute>
              } 
            />
            <Route path="/contact" element={<ContactPage />} />
          </Routes>
        </>
      </Router>
    </AuthProvider>
  );
}

export default App;