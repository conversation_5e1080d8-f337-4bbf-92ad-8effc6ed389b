# Server.py Analysis & Refactoring Plan

## 📊 **Current Structure Analysis**

### **File Size & Complexity**
- **Total Lines**: 1,699 lines
- **Total Endpoints**: 62 endpoints
- **Models**: 30+ Pydantic models
- **Status**: Monolithic, needs systematic refactoring

### **📋 Endpoint Categories (62 total)**

#### **🔐 Authentication (4 endpoints)**
- `POST /auth/register` - User registration
- `POST /auth/login` - User login  
- `GET /auth/me` - Get current user
- `POST /auth/create-super-admin` - Create super admin

#### **📚 Course Management (8 endpoints)**
- `POST /courses` - Create course
- `GET /courses` - List courses (public)
- `GET /courses/{course_id}` - Get course details
- `PUT /courses/{course_id}` - Update course
- `PUT /courses/{course_id}/publish` - Publish course
- `POST /courses/{course_id}/lessons` - Create lesson
- `GET /courses/{course_id}/lessons` - Get course lessons
- `GET /lessons/{lesson_id}` - Get lesson details

#### **🎓 Enrollment & Progress (4 endpoints)**
- `POST /courses/{course_id}/enroll` - Enroll in course
- `GET /my-courses` - Get user's courses
- `GET /courses/{course_id}/progress` - Get course progress
- `POST /lessons/{lesson_id}/complete` - Mark lesson complete

#### **📊 Dashboard (1 endpoint)**
- `GET /dashboard/stats` - Dashboard statistics

#### **💬 Discussions & Reviews (4 endpoints)**
- `POST /courses/{course_id}/discussions` - Create discussion
- `GET /courses/{course_id}/discussions` - Get discussions
- `POST /courses/{course_id}/reviews` - Create review
- `GET /courses/{course_id}/reviews` - Get reviews

#### **📧 Newsletter (4 endpoints)**
- `POST /newsletter/subscribe` - Subscribe to newsletter
- `GET /newsletter/subscribers` - Get subscribers (admin)
- `GET /newsletter/integration-guide` - Integration guide
- `POST /newsletter/subscribe` - (Duplicate endpoint)

#### **🎛️ Admin Analytics & Content (4 endpoints)**
- `GET /admin/analytics` - Admin analytics
- `GET /admin/content` - Site content management
- `PUT /admin/content/{content_key}` - Update content
- `GET /admin/webinars` - Admin webinars
- `POST /admin/webinars` - Create webinar
- `GET /webinars` - Public webinars

#### **👥 User Management (4 endpoints)**
- `GET /admin/users` - Get all users
- `POST /admin/users` - Create user (admin)
- `PUT /admin/users/{user_id}` - Update user
- `DELETE /admin/users/{user_id}` - Delete user

#### **🎨 Branding Management (3 endpoints)**
- `GET /admin/branding` - Get branding settings
- `PUT /admin/branding` - Update branding
- `GET /branding` - Public branding

#### **👨‍🏫 Instructor Management (8 endpoints)**
- `GET /admin/instructor-page` - Get instructor page content
- `PUT /admin/instructor-page` - Update instructor page
- `GET /instructor-page-content` - Public instructor page
- `GET /admin/instructor-profiles` - Get instructor profiles
- `POST /admin/instructor-profiles` - Create instructor profile
- `PUT /admin/instructor-profiles/{profile_id}` - Update profile
- `DELETE /admin/instructor-profiles/{profile_id}` - Delete profile
- `GET /instructor-profiles` - Public instructor profiles
- `POST /admin/instructor-profiles/init-default` - Init default profiles

#### **💬 Testimonials Management (6 endpoints)**
- `GET /admin/testimonials` - Get testimonials
- `POST /admin/testimonials` - Create testimonial
- `PUT /admin/testimonials/{testimonial_id}` - Update testimonial
- `DELETE /admin/testimonials/{testimonial_id}` - Delete testimonial
- `GET /testimonials` - Public testimonials
- `POST /admin/testimonials/init-default` - Init default testimonials

#### **📞 Contact Management (7 endpoints)**
- `POST /contact/submit` - Submit contact form
- `GET /admin/contact-submissions` - Get contact submissions
- `PUT /admin/contact-submissions/{submission_id}/mark-read` - Mark read
- `GET /admin/contact-info` - Get contact info
- `PUT /admin/contact-info` - Update contact info
- `GET /contact-info` - Public contact info

#### **🔗 Social Links Management (3 endpoints)**
- `GET /admin/social-links` - Get social links
- `PUT /admin/social-links` - Update social links
- `GET /social-links` - Public social links

## 🎯 **Refactoring Strategy**

### **Phase 1: Identify Missing Endpoints in simple_server.py**
Compare server.py endpoints with simple_server.py to identify gaps.

### **Phase 2: Modular Structure**
Break server.py into logical modules:
- `auth.py` - Authentication routes
- `courses.py` - Course management
- `admin.py` - Admin panel routes
- `public.py` - Public API routes
- `models.py` - All Pydantic models

### **Phase 3: Database Compatibility**
Fix database wrapper compatibility issues between server.py and simple_server.py.

### **Phase 4: Gradual Migration**
Systematically merge working features from simple_server.py into refactored server.py.
