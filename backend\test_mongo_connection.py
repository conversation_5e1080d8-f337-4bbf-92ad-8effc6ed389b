#!/usr/bin/env python3
"""
MongoDB Connection Test Script
Tests the MongoDB Atlas connection with current credentials
"""

import os
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ServerSelectionTimeoutError, ConfigurationError, OperationFailure
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_mongodb_connection():
    """Test MongoDB connection with detailed error reporting"""
    
    mongo_url = os.getenv("MONGO_URL")
    db_name = os.getenv("DB_NAME", "lmsdb")
    
    print("🔍 MongoDB Connection Test")
    print("=" * 50)
    print(f"Database Name: {db_name}")
    print(f"Connection URL: {mongo_url[:50]}...")
    print()
    
    if not mongo_url:
        print("❌ ERROR: MONGO_URL not found in environment variables")
        return False
    
    try:
        print("🔄 Attempting to connect to MongoDB Atlas...")
        
        # Create client with shorter timeout for testing
        client = AsyncIOMotorClient(
            mongo_url,
            serverSelectionTimeoutMS=10000,  # 10 seconds
            connectTimeoutMS=10000,
            socketTimeoutMS=10000,
            maxPoolSize=1
        )
        
        # Get database
        db = client[db_name]
        
        print("🔄 Testing server connection...")
        
        # Test connection by running a simple command
        server_info = await client.server_info()
        print(f"✅ Connected successfully!")
        print(f"MongoDB Version: {server_info.get('version', 'Unknown')}")
        
        print("\n🔄 Testing database access...")
        
        # Test database operations
        collections = await db.list_collection_names()
        print(f"✅ Database access successful!")
        print(f"Collections found: {len(collections)}")
        if collections:
            print(f"Collection names: {', '.join(collections[:5])}")
        
        print("\n🔄 Testing write operations...")
        
        # Test write operation
        test_collection = db.connection_test
        test_doc = {"test": True, "timestamp": "2024-01-01"}
        result = await test_collection.insert_one(test_doc)
        print(f"✅ Write test successful! Document ID: {result.inserted_id}")
        
        # Clean up test document
        await test_collection.delete_one({"_id": result.inserted_id})
        print("✅ Cleanup successful!")
        
        # Close connection
        client.close()
        
        print("\n🎉 All tests passed! MongoDB connection is working properly.")
        return True
        
    except ServerSelectionTimeoutError as e:
        print(f"❌ Server Selection Timeout Error:")
        print(f"   This usually means:")
        print(f"   - Network connectivity issues")
        print(f"   - Firewall blocking the connection")
        print(f"   - MongoDB Atlas IP whitelist restrictions")
        print(f"   - Invalid connection string")
        print(f"\n   Error details: {str(e)}")
        return False
        
    except ConfigurationError as e:
        print(f"❌ Configuration Error:")
        print(f"   This usually means:")
        print(f"   - Invalid connection string format")
        print(f"   - Missing required parameters")
        print(f"\n   Error details: {str(e)}")
        return False
        
    except OperationFailure as e:
        print(f"❌ Operation Failure:")
        print(f"   This usually means:")
        print(f"   - Authentication failed")
        print(f"   - Insufficient permissions")
        print(f"   - Database access denied")
        print(f"\n   Error details: {str(e)}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected Error:")
        print(f"   Error type: {type(e).__name__}")
        print(f"   Error details: {str(e)}")
        return False

async def test_connection_variations():
    """Test different connection string variations"""
    
    base_url = os.getenv("MONGO_URL")
    if not base_url:
        return
    
    print("\n🔄 Testing connection string variations...")
    
    # Test with SSL disabled
    url_no_ssl = base_url.replace("?", "?ssl=false&")
    print(f"\n🔄 Testing without SSL...")
    
    try:
        client = AsyncIOMotorClient(url_no_ssl, serverSelectionTimeoutMS=5000)
        await client.server_info()
        print("✅ Connection without SSL successful!")
        client.close()
    except Exception as e:
        print(f"❌ Connection without SSL failed: {str(e)[:100]}...")
    
    # Test with different TLS version
    url_tls = base_url.replace("?", "?tls=true&tlsAllowInvalidCertificates=true&")
    print(f"\n🔄 Testing with TLS options...")
    
    try:
        client = AsyncIOMotorClient(url_tls, serverSelectionTimeoutMS=5000)
        await client.server_info()
        print("✅ Connection with TLS options successful!")
        client.close()
    except Exception as e:
        print(f"❌ Connection with TLS options failed: {str(e)[:100]}...")

def check_network_connectivity():
    """Check basic network connectivity"""
    import socket
    
    print("\n🔄 Testing network connectivity...")
    
    # Extract hostname from MongoDB URL
    mongo_url = os.getenv("MONGO_URL", "")
    if "mongodb+srv://" in mongo_url:
        # Extract cluster hostname
        try:
            hostname = mongo_url.split("@")[1].split("/")[0].split("?")[0]
            print(f"Testing connectivity to: {hostname}")
            
            # Test DNS resolution
            try:
                ip = socket.gethostbyname(hostname)
                print(f"✅ DNS resolution successful: {hostname} -> {ip}")
            except socket.gaierror as e:
                print(f"❌ DNS resolution failed: {e}")
                return False
            
            # Test port connectivity (MongoDB default port 27017)
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((hostname, 27017))
                sock.close()
                
                if result == 0:
                    print(f"✅ Port 27017 is accessible")
                else:
                    print(f"❌ Port 27017 is not accessible (error code: {result})")
                    return False
                    
            except Exception as e:
                print(f"❌ Port connectivity test failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Could not parse hostname from URL: {e}")
            return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 Starting MongoDB Connection Diagnostics")
    print("=" * 60)
    
    # Check network connectivity first
    network_ok = check_network_connectivity()
    
    if not network_ok:
        print("\n❌ Network connectivity issues detected.")
        print("   Please check your internet connection and firewall settings.")
        return
    
    # Test main connection
    connection_ok = await test_mongodb_connection()
    
    if not connection_ok:
        # Try alternative connection methods
        await test_connection_variations()
        
        print("\n" + "=" * 60)
        print("🔧 TROUBLESHOOTING SUGGESTIONS:")
        print("=" * 60)
        print("1. Check MongoDB Atlas Network Access:")
        print("   - Go to MongoDB Atlas Dashboard")
        print("   - Navigate to Network Access")
        print("   - Add your current IP address (0.0.0.0/0 for testing)")
        print()
        print("2. Verify Database User Permissions:")
        print("   - Go to Database Access in Atlas")
        print("   - Ensure user has readWrite permissions")
        print()
        print("3. Check Connection String:")
        print("   - Verify username and password are correct")
        print("   - Ensure cluster name is correct")
        print()
        print("4. Try Local MongoDB Setup:")
        print("   - Install MongoDB locally for development")
        print("   - Use connection string: mongodb://localhost:27017/lmsdb")

if __name__ == "__main__":
    asyncio.run(main())
