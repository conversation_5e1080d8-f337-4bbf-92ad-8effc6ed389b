"""
Mock database for development when MongoDB is not available
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import bcrypt

class MockDatabase:
    def __init__(self, data_file="mock_data.json"):
        self.data_file = data_file
        self.data = self._load_data()
        
    def _load_data(self) -> Dict[str, List[Dict]]:
        """Load data from JSON file or create default data"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        # Default data with a test user
        default_data = {
            "users": [
                {
                    "id": "test-user-1",
                    "email": "<EMAIL>",
                    "password": bcrypt.hashpw("test123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
                    "name": "Test User",
                    "role": "student",
                    "created_at": datetime.utcnow().isoformat(),
                    "bio": None,
                    "avatar_url": None
                },
                {
                    "id": "admin-user-1",
                    "email": "<EMAIL>",
                    "password": bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
                    "name": "Admin User",
                    "role": "admin",
                    "created_at": datetime.utcnow().isoformat(),
                    "bio": None,
                    "avatar_url": None
                },
                {
                    "id": "instructor-user-1",
                    "email": "<EMAIL>",
                    "password": bcrypt.hashpw("instructor123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
                    "name": "Instructor User",
                    "role": "instructor",
                    "created_at": datetime.utcnow().isoformat(),
                    "bio": "Experienced AI instructor",
                    "avatar_url": None
                }
            ],
            "courses": [],
            "lessons": [],
            "enrollments": [],
            "progress": [],
            "discussions": [],
            "reviews": [],
            "newsletter_subscribers": [],
            "site_content": [],
            "webinars": [],
            "branding_settings": [],
            "instructor_page_content": [],
            "instructor_profiles": [],
            "testimonials": [],
            "contact_submissions": [],
            "contact_info": [],
            "social_links": []
        }
        
        self._save_data(default_data)
        return default_data
    
    def _save_data(self, data: Dict[str, List[Dict]]):
        """Save data to JSON file"""
        try:
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving data: {e}")

class MockCollection:
    def __init__(self, db: MockDatabase, collection_name: str):
        self.db = db
        self.collection_name = collection_name
        
    async def find_one(self, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find one document matching the query"""
        collection_data = self.db.data.get(self.collection_name, [])
        
        for doc in collection_data:
            if self._matches_query(doc, query):
                return doc
        return None
    
    async def find(self, query: Dict[str, Any] = None):
        """Find documents matching the query"""
        return MockCursor(self.db, self.collection_name, query or {})
    
    async def insert_one(self, document: Dict[str, Any]):
        """Insert a single document"""
        if self.collection_name not in self.db.data:
            self.db.data[self.collection_name] = []
        
        # Convert datetime objects to strings
        doc_copy = self._serialize_document(document)
        self.db.data[self.collection_name].append(doc_copy)
        self.db._save_data(self.db.data)
        return MockInsertResult(doc_copy.get("id"))
    
    async def update_one(self, query: Dict[str, Any], update: Dict[str, Any]):
        """Update a single document"""
        collection_data = self.db.data.get(self.collection_name, [])
        
        for i, doc in enumerate(collection_data):
            if self._matches_query(doc, query):
                if "$set" in update:
                    doc.update(self._serialize_document(update["$set"]))
                elif "$inc" in update:
                    for key, value in update["$inc"].items():
                        doc[key] = doc.get(key, 0) + value
                self.db._save_data(self.db.data)
                return MockUpdateResult(1)
        return MockUpdateResult(0)
    
    async def delete_one(self, query: Dict[str, Any]):
        """Delete a single document"""
        collection_data = self.db.data.get(self.collection_name, [])
        
        for i, doc in enumerate(collection_data):
            if self._matches_query(doc, query):
                del collection_data[i]
                self.db._save_data(self.db.data)
                return MockDeleteResult(1)
        return MockDeleteResult(0)
    
    async def count_documents(self, query: Dict[str, Any] = None) -> int:
        """Count documents matching the query"""
        collection_data = self.db.data.get(self.collection_name, [])
        if not query:
            return len(collection_data)
        
        count = 0
        for doc in collection_data:
            if self._matches_query(doc, query):
                count += 1
        return count
    
    def _matches_query(self, document: Dict[str, Any], query: Dict[str, Any]) -> bool:
        """Check if document matches the query"""
        for key, value in query.items():
            if key == "$in":
                continue
            if key not in document:
                return False
            if isinstance(value, dict):
                if "$in" in value:
                    if document[key] not in value["$in"]:
                        return False
                elif "$ne" in value:
                    if document[key] == value["$ne"]:
                        return False
                elif "$gte" in value:
                    if document[key] < value["$gte"]:
                        return False
            else:
                if document[key] != value:
                    return False
        return True
    
    def _serialize_document(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Convert datetime objects to strings for JSON serialization"""
        result = {}
        for key, value in document.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result

class MockCursor:
    def __init__(self, db: MockDatabase, collection_name: str, query: Dict[str, Any]):
        self.db = db
        self.collection_name = collection_name
        self.query = query
        self._sort_key = None
        self._sort_direction = 1
        
    def sort(self, key: str, direction: int = 1):
        """Sort the results"""
        self._sort_key = key
        self._sort_direction = direction
        return self
    
    def limit(self, count: int):
        """Limit the number of results"""
        self._limit = count
        return self
    
    async def to_list(self, length: int) -> List[Dict[str, Any]]:
        """Convert cursor to list"""
        collection_data = self.db.data.get(self.collection_name, [])
        results = []
        
        for doc in collection_data:
            if self._matches_query(doc, self.query):
                results.append(doc)
        
        # Apply sorting
        if self._sort_key:
            results.sort(key=lambda x: x.get(self._sort_key, ""), reverse=(self._sort_direction == -1))
        
        return results[:length]
    
    def _matches_query(self, document: Dict[str, Any], query: Dict[str, Any]) -> bool:
        """Check if document matches the query"""
        for key, value in query.items():
            if key not in document:
                return False
            if isinstance(value, dict):
                if "$in" in value:
                    if document[key] not in value["$in"]:
                        return False
            else:
                if document[key] != value:
                    return False
        return True

class MockInsertResult:
    def __init__(self, inserted_id):
        self.inserted_id = inserted_id

class MockUpdateResult:
    def __init__(self, modified_count):
        self.modified_count = modified_count

class MockDeleteResult:
    def __init__(self, deleted_count):
        self.deleted_count = deleted_count

# Create mock database instance
mock_db_instance = MockDatabase()

class MockDB:
    def __init__(self):
        self.users = MockCollection(mock_db_instance, "users")
        self.courses = MockCollection(mock_db_instance, "courses")
        self.lessons = MockCollection(mock_db_instance, "lessons")
        self.enrollments = MockCollection(mock_db_instance, "enrollments")
        self.progress = MockCollection(mock_db_instance, "progress")
        self.discussions = MockCollection(mock_db_instance, "discussions")
        self.reviews = MockCollection(mock_db_instance, "reviews")
        self.newsletter_subscribers = MockCollection(mock_db_instance, "newsletter_subscribers")
        self.site_content = MockCollection(mock_db_instance, "site_content")
        self.webinars = MockCollection(mock_db_instance, "webinars")
        self.branding_settings = MockCollection(mock_db_instance, "branding_settings")
        self.instructor_page_content = MockCollection(mock_db_instance, "instructor_page_content")
        self.instructor_profiles = MockCollection(mock_db_instance, "instructor_profiles")
        self.testimonials = MockCollection(mock_db_instance, "testimonials")
        self.contact_submissions = MockCollection(mock_db_instance, "contact_submissions")
        self.contact_info = MockCollection(mock_db_instance, "contact_info")
        self.social_links = MockCollection(mock_db_instance, "social_links")

# Export the mock database
mock_db = MockDB()
