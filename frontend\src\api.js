import axios from 'axios';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API_URL = `${BACKEND_URL}/api`;

const api = axios.create({
  baseURL: API_URL,
});

// Function to set the authorization header
export const setAuthToken = (token) => {
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete api.defaults.headers.common['Authorization'];
  }
};

// Initialize the token from localStorage
const token = localStorage.getItem('token');
if (token) {
  setAuthToken(token);
}

// Auth API calls
export const fetchUser = () => api.get('/auth/me');
export const login = (email, password) => api.post('/auth/login', { email, password });
export const register = (email, password, name, role) => api.post('/auth/register', { email, password, name, role });

// Newsletter API calls
export const subscribeNewsletter = (email, name) => api.post('/newsletter/subscribe', { email, name });

// Branding API calls
export const fetchBranding = () => api.get('/branding');

// Course API calls
export const fetchCourses = () => api.get('/courses');
export const fetchCourse = (courseId) => api.get(`/courses/${courseId}`);
export const fetchCourseLessons = (courseId) => api.get(`/courses/${courseId}/lessons`);
export const enrollInCourse = (courseId) => api.post(`/courses/${courseId}/enroll`);
export const fetchMyCourses = () => api.get('/my-courses');
export const createCourse = (courseData) => api.post('/courses', courseData);


// Dashboard API calls
export const fetchDashboardStats = () => api.get('/dashboard/stats');

// Testimonials API calls
export const fetchTestimonials = () => api.get('/testimonials');

// Instructor Profiles API calls
export const fetchInstructorProfiles = () => api.get('/instructor-profiles');

// Contact API calls
export const fetchContactInfo = () => api.get('/contact-info');
export const fetchSocialLinks = () => api.get('/social-links');
export const submitContactForm = (formData) => api.post('/contact/submit', formData);

// Admin API calls
export const fetchAdminAnalytics = () => api.get('/admin/analytics');
export const fetchSiteContent = () => api.get('/admin/content');
export const updateSiteContent = (contentKey, content_value) => api.put(`/admin/content/${contentKey}`, { content_value });
export const fetchAdminUsers = () => api.get('/admin/users');
export const createAdminUser = (userData) => api.post('/admin/users', userData);
export const updateAdminUser = (userId, userData) => api.put(`/admin/users/${userId}`, userData);
export const deleteAdminUser = (userId) => api.delete(`/admin/users/${userId}`);
export const fetchAdminWebinars = () => api.get('/admin/webinars');
export const createAdminWebinar = (webinarData) => api.post('/admin/webinars', webinarData);
export const fetchNewsletterSubscribers = () => api.get('/newsletter/subscribers');
export const fetchAdminBranding = () => api.get('/admin/branding');
export const updateAdminBranding = (brandingData) => api.put('/admin/branding', brandingData);
export const fetchInstructorPageContent = () => api.get('/admin/instructor-page');
export const updateInstructorPageContent = (pageData) => api.put('/admin/instructor-page', pageData);
export const fetchAdminInstructorProfiles = () => api.get('/admin/instructor-profiles');
export const createInstructorProfile = (profileData) => api.post('/admin/instructor-profiles', profileData);
export const updateInstructorProfile = (profileId, profileData) => api.put(`/admin/instructor-profiles/${profileId}`, profileData);
export const deleteInstructorProfile = (profileId) => api.delete(`/admin/instructor-profiles/${profileId}`);
export const initDefaultInstructorProfiles = () => api.post('/admin/instructor-profiles/init-default');
export const fetchAdminTestimonials = () => api.get('/admin/testimonials');
export const createTestimonial = (testimonialData) => api.post('/admin/testimonials', testimonialData);
export const updateTestimonial = (testimonialId, testimonialData) => api.put(`/admin/testimonials/${testimonialId}`, testimonialData);
export const deleteTestimonial = (testimonialId) => api.delete(`/admin/testimonials/${testimonialId}`);
export const initDefaultTestimonials = () => api.post('/admin/testimonials/init-default');


export default api;
