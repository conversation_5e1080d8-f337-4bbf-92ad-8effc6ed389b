import React, { useState, useEffect } from 'react';
import { setAuthToken, fetchUser as apiFetchUser, login as apiLogin, register as apiRegister } from '../api';

// Auth Context
const AuthContext = React.createContext();

export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      setAuthToken(token);
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await apiFetchUser();
      setUser(response.data);
    } catch (error) {
      localStorage.removeItem('token');
      setAuthToken(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    const response = await apiLogin(email, password);
    const { access_token, user: userData } = response.data;
    
    localStorage.setItem('token', access_token);
    setAuthToken(access_token);
    setUser(userData);
    
    return userData;
  };

  const register = async (email, password, name, role = 'student') => {
    const response = await apiRegister(email, password, name, role);
    const { access_token, user: userData } = response.data;
    
    localStorage.setItem('token', access_token);
    setAuthToken(access_token);
    setUser(userData);
    
    return userData;
  };

  const logout = () => {
    localStorage.removeItem('token');
    setAuthToken(null);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
