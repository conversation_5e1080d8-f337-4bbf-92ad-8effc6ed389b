"""
Development Mock Data
Provides mock data when MongoDB is not available
"""

from datetime import datetime
from typing import List, Dict, Any

# Mock Users Data
MOCK_USERS = [
    {
        "id": "admin-001",
        "email": "<EMAIL>",
        "name": "Super Admin",
        "role": "admin",
        "password": "$2b$12$ZgNSMVAYeKlnYKa5mIcg0..F/OWXK03dzm5YhK3ALsZeWujDhmvVS",  # Admin123!
        "created_at": datetime.utcnow(),
        "is_active": True
    },
    {
        "id": "instructor-001",
        "email": "<EMAIL>",
        "name": "Dr. <PERSON>",
        "role": "instructor",
        "password": "$2b$12$ZgNSMVAYeKlnYKa5mIcg0..F/OWXK03dzm5YhK3ALsZeWujDhmvVS",  # Admin123!
        "created_at": datetime.utcnow(),
        "is_active": True
    },
    {
        "id": "student-001",
        "email": "<EMAIL>",
        "name": "<PERSON>",
        "role": "student",
        "password": "$2b$12$ZgNSMVAYeKlnYKa5mIcg0..F/OWXK03dzm5YhK3ALsZeWujDhmvVS",  # Admin123!
        "created_at": datetime.utcnow(),
        "is_active": True
    }
]

# Mock Courses Data
MOCK_COURSES = [
    {
        "id": "course-001",
        "title": "Introduction to Machine Learning",
        "description": "Learn the fundamentals of machine learning with hands-on projects and real-world applications.",
        "instructor_id": "instructor-001",
        "instructor_name": "Dr. Sarah Johnson",
        "price": 99.99,
        "duration_weeks": 8,
        "difficulty_level": "beginner",
        "category": "Machine Learning",
        "is_published": True,
        "student_count": 156,
        "rating": 4.8,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    },
    {
        "id": "course-002",
        "title": "Deep Learning with PyTorch",
        "description": "Master deep learning concepts and build neural networks using PyTorch framework.",
        "instructor_id": "instructor-001",
        "instructor_name": "Dr. Sarah Johnson",
        "price": 149.99,
        "duration_weeks": 12,
        "difficulty_level": "intermediate",
        "category": "Deep Learning",
        "is_published": True,
        "student_count": 89,
        "rating": 4.9,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    },
    {
        "id": "course-003",
        "title": "Natural Language Processing",
        "description": "Explore NLP techniques and build text processing applications with modern AI tools.",
        "instructor_id": "instructor-001",
        "instructor_name": "Dr. Sarah Johnson",
        "price": 0,  # Free course
        "duration_weeks": 6,
        "difficulty_level": "intermediate",
        "category": "NLP",
        "is_published": True,
        "student_count": 234,
        "rating": 4.7,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }
]

# Mock Branding Data
MOCK_BRANDING = {
    "site_title": "AI Academy",
    "site_description": "Master AI with Expert-Led Courses",
    "logo_url": "https://via.placeholder.com/150x150/4F46E5/FFFFFF?text=AI",
    "primary_color": "#4F46E5",
    "secondary_color": "#7C3AED",
    "contact_email": "<EMAIL>",
    "social_links": {
        "twitter": "https://twitter.com/aiacademy",
        "linkedin": "https://linkedin.com/company/aiacademy",
        "github": "https://github.com/aiacademy"
    }
}

# Mock Instructor Profiles
MOCK_INSTRUCTOR_PROFILES = [
    {
        "id": "profile-001",
        "instructor_id": "instructor-001",
        "name": "Dr. Sarah Johnson",
        "title": "AI Research Scientist",
        "bio": "Dr. Johnson has over 10 years of experience in AI research and has published 50+ papers in top-tier conferences.",
        "avatar_url": "https://via.placeholder.com/200x200/4F46E5/FFFFFF?text=SJ",
        "expertise": ["Machine Learning", "Deep Learning", "Computer Vision"],
        "experience_years": 10,
        "courses_count": 3,
        "students_count": 479,
        "rating": 4.8,
        "is_active": True,
        "order": 1
    }
]

# Mock Lessons Data
MOCK_LESSONS = [
    {
        "id": "lesson-001",
        "course_id": "course-001",
        "title": "Introduction to Machine Learning",
        "description": "Learn the fundamentals of machine learning and its applications",
        "content": "<h2>Welcome to Machine Learning</h2><p>Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.</p><h3>Key Concepts:</h3><ul><li>Supervised Learning</li><li>Unsupervised Learning</li><li>Reinforcement Learning</li></ul>",
        "type": "video",
        "duration_minutes": 45,
        "order": 1,
        "is_published": True
    },
    {
        "id": "lesson-002",
        "course_id": "course-001",
        "title": "Setting Up Your Environment",
        "description": "Install and configure Python, Jupyter, and essential ML libraries",
        "content": "<h2>Development Environment Setup</h2><p>Let's set up your machine learning development environment.</p><h3>Required Tools:</h3><ul><li>Python 3.8+</li><li>Jupyter Notebook</li><li>NumPy, Pandas, Scikit-learn</li></ul>",
        "type": "text",
        "duration_minutes": 30,
        "order": 2,
        "is_published": True
    },
    {
        "id": "lesson-003",
        "course_id": "course-002",
        "title": "Neural Network Fundamentals",
        "description": "Understanding the building blocks of neural networks",
        "content": "<h2>Neural Networks Explained</h2><p>Neural networks are computing systems inspired by biological neural networks.</p>",
        "type": "video",
        "duration_minutes": 60,
        "order": 1,
        "is_published": True
    }
]

# Mock Quizzes Data
MOCK_QUIZZES = [
    {
        "id": "quiz-001",
        "course_id": "course-001",
        "title": "ML Fundamentals Quiz",
        "description": "Test your understanding of machine learning basics",
        "questions": [
            {
                "id": "q1",
                "type": "multiple_choice",
                "question": "What is machine learning?",
                "options": [
                    "A type of computer hardware",
                    "A subset of AI that learns from data",
                    "A programming language",
                    "A database system"
                ],
                "correct_answer": 1,
                "explanation": "Machine learning is indeed a subset of artificial intelligence that enables systems to learn from data."
            },
            {
                "id": "q2",
                "type": "true_false",
                "question": "Supervised learning requires labeled training data.",
                "correct_answer": True,
                "explanation": "Supervised learning algorithms learn from labeled examples to make predictions on new data."
            }
        ],
        "timeLimit": 15,
        "passingScore": 70,
        "attempts": 3,
        "is_published": True
    },
    {
        "id": "quiz-002",
        "course_id": "course-002",
        "title": "Deep Learning Concepts",
        "description": "Advanced quiz on neural networks and deep learning",
        "questions": [
            {
                "id": "q1",
                "type": "multiple_choice",
                "question": "What is a neural network?",
                "options": [
                    "A biological brain",
                    "A computing system inspired by biological neural networks",
                    "A type of database",
                    "A web framework"
                ],
                "correct_answer": 1,
                "explanation": "Neural networks are computing systems inspired by biological neural networks."
            }
        ],
        "timeLimit": 20,
        "passingScore": 80,
        "attempts": 2,
        "is_published": True
    }
]

# Mock Discussions Data
MOCK_DISCUSSIONS = [
    {
        "id": "discussion-001",
        "course_id": "course-001",
        "title": "Getting Started with ML - Questions & Tips",
        "content": "Welcome to the course! Feel free to ask any questions about machine learning fundamentals here.",
        "author_id": "instructor-001",
        "author_name": "Dr. Sarah Johnson",
        "author_role": "instructor",
        "created_at": "2024-01-15T10:00:00Z",
        "updated_at": "2024-01-15T10:00:00Z",
        "likes": 5,
        "replies": [
            {
                "id": "reply-001",
                "content": "Great course so far! The explanations are very clear.",
                "author_id": "student-001",
                "author_name": "John Doe",
                "author_role": "student",
                "created_at": "2024-01-15T14:30:00Z",
                "likes": 2
            }
        ],
        "tags": ["welcome", "questions", "ml-basics"],
        "is_pinned": True
    },
    {
        "id": "discussion-002",
        "course_id": "course-001",
        "title": "Recommended Resources for Further Learning",
        "content": "Here are some additional resources I recommend for deepening your ML knowledge...",
        "author_id": "instructor-001",
        "author_name": "Dr. Sarah Johnson",
        "author_role": "instructor",
        "created_at": "2024-01-16T09:00:00Z",
        "updated_at": "2024-01-16T09:00:00Z",
        "likes": 8,
        "replies": [],
        "tags": ["resources", "books", "papers"],
        "is_pinned": False
    }
]

# Mock Reviews Data
MOCK_REVIEWS = [
    {
        "id": "review-001",
        "course_id": "course-001",
        "user_id": "student-001",
        "user_name": "John Doe",
        "rating": 5,
        "review": "Excellent course! Dr. Johnson explains complex concepts in a very understandable way. The hands-on projects were particularly valuable.",
        "created_at": "2024-01-20T16:45:00Z",
        "is_verified": True
    },
    {
        "id": "review-002",
        "course_id": "course-001",
        "user_id": "student-002",
        "user_name": "Jane Smith",
        "rating": 4,
        "review": "Great content and well-structured. Would love to see more practical examples in future lessons.",
        "created_at": "2024-01-18T11:20:00Z",
        "is_verified": True
    },
    {
        "id": "review-003",
        "course_id": "course-002",
        "user_id": "student-001",
        "user_name": "John Doe",
        "rating": 5,
        "review": "Deep learning concepts are explained brilliantly. The neural network visualizations really helped me understand the material.",
        "created_at": "2024-01-22T14:15:00Z",
        "is_verified": True
    }
]

class MockDatabase:
    """Mock database class that simulates MongoDB operations"""
    
    def __init__(self):
        self.users = MOCK_USERS.copy()
        self.courses = MOCK_COURSES.copy()
        self.branding_settings = [MOCK_BRANDING]
        self.instructor_profiles = MOCK_INSTRUCTOR_PROFILES.copy()
        self.enrollments = []
        self.lessons = MOCK_LESSONS.copy()
        self.quizzes = MOCK_QUIZZES.copy()
        self.progress = []
        self.discussions = MOCK_DISCUSSIONS.copy()
        self.reviews = MOCK_REVIEWS.copy()
        self.newsletter_subscribers = []
    
    def find_one(self, collection: str, query: Dict[str, Any]) -> Dict[str, Any] | None:
        """Mock find_one operation"""
        data = getattr(self, collection, [])
        
        if not data:
            return None
            
        if isinstance(data, list):
            for item in data:
                if self._matches_query(item, query):
                    return item
        
        return None
    
    def find(self, collection: str, query: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Mock find operation"""
        data = getattr(self, collection, [])
        
        if not isinstance(data, list):
            return []
        
        if query is None:
            return data.copy()
        
        return [item for item in data if self._matches_query(item, query)]
    
    def insert_one(self, collection: str, document: Dict[str, Any]) -> str:
        """Mock insert_one operation"""
        data = getattr(self, collection, [])
        
        if not isinstance(data, list):
            setattr(self, collection, [])
            data = getattr(self, collection)
        
        # Generate ID if not present
        if 'id' not in document:
            document['id'] = f"{collection}-{len(data) + 1:03d}"
        
        data.append(document.copy())
        return document['id']
    
    def update_one(self, collection: str, query: Dict[str, Any], update: Dict[str, Any]) -> bool:
        """Mock update_one operation"""
        data = getattr(self, collection, [])
        
        if not isinstance(data, list):
            return False
        
        for i, item in enumerate(data):
            if self._matches_query(item, query):
                if '$set' in update:
                    item.update(update['$set'])
                elif '$inc' in update:
                    for key, value in update['$inc'].items():
                        item[key] = item.get(key, 0) + value
                else:
                    item.update(update)
                return True
        
        return False
    
    def delete_one(self, collection: str, query: Dict[str, Any]) -> bool:
        """Mock delete_one operation"""
        data = getattr(self, collection, [])
        
        if not isinstance(data, list):
            return False
        
        for i, item in enumerate(data):
            if self._matches_query(item, query):
                data.pop(i)
                return True
        
        return False
    
    def count_documents(self, collection: str, query: Dict[str, Any] = None) -> int:
        """Mock count_documents operation"""
        return len(self.find(collection, query))
    
    def _matches_query(self, item: Dict[str, Any], query: Dict[str, Any]) -> bool:
        """Check if item matches query"""
        for key, value in query.items():
            if key not in item:
                return False
            
            if isinstance(value, dict):
                # Handle operators like $in, $ne, etc.
                if '$in' in value:
                    if item[key] not in value['$in']:
                        return False
                elif '$ne' in value:
                    if item[key] == value['$ne']:
                        return False
                else:
                    return False
            else:
                if item[key] != value:
                    return False
        
        return True

# Global mock database instance
mock_db = MockDatabase()
