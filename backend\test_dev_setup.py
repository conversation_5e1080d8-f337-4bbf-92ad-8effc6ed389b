#!/usr/bin/env python3
"""
Test Development Setup
Tests the mock database and creates a super admin account
"""

import asyncio
import os
from dotenv import load_dotenv
from database import database
import bcrypt

load_dotenv()

async def test_mock_database():
    """Test the mock database functionality"""
    print("🧪 Testing Mock Database Setup")
    print("=" * 50)
    
    # Connect to database (will use mock data in DEV_MODE)
    await database.connect()
    
    # Test users collection
    users = database.get_collection("users")
    
    print("🔍 Testing user queries...")
    
    # Find admin user
    admin_user = await users.find_one({"role": "admin"})
    if admin_user:
        print(f"✅ Found admin user: {admin_user['email']}")
    else:
        print("❌ No admin user found")
    
    # Find all users
    all_users_cursor = users.find({})
    all_users = await all_users_cursor.to_list(100)
    print(f"✅ Found {len(all_users)} total users")
    
    # Test courses collection
    courses = database.get_collection("courses")
    all_courses_cursor = courses.find({"is_published": True})
    published_courses = await all_courses_cursor.to_list(100)
    print(f"✅ Found {len(published_courses)} published courses")
    
    # Test branding collection
    branding = database.get_collection("branding_settings")
    branding_data = await branding.find_one({})
    if branding_data:
        print(f"✅ Found branding data: {branding_data['site_title']}")
    
    print("\n🎉 Mock database test completed successfully!")
    return True

async def create_super_admin():
    """Create super admin account for testing"""
    print("\n🔧 Creating Super Admin Account")
    print("=" * 50)
    
    users = database.get_collection("users")
    
    # Check if admin already exists
    existing_admin = await users.find_one({"role": "admin"})
    if existing_admin:
        print(f"✅ Super admin already exists: {existing_admin['email']}")
        return existing_admin
    
    # Create new super admin
    admin_email = "<EMAIL>"
    admin_password = "Admin123!"
    
    # Hash password
    hashed_password = bcrypt.hashpw(admin_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    admin_data = {
        "email": admin_email,
        "name": "Super Admin",
        "role": "admin",
        "password": hashed_password,
        "is_active": True
    }
    
    result = await users.insert_one(admin_data)
    print(f"✅ Super admin created successfully!")
    print(f"   Email: {admin_email}")
    print(f"   Password: {admin_password}")
    print(f"   ID: {result.inserted_id}")
    
    return admin_data

async def test_login_credentials():
    """Test login with the created credentials"""
    print("\n🔐 Testing Login Credentials")
    print("=" * 50)
    
    users = database.get_collection("users")
    
    # Test admin login
    admin_user = await users.find_one({"email": "<EMAIL>"})
    if admin_user:
        # Verify password
        test_password = "Admin123!"
        if bcrypt.checkpw(test_password.encode('utf-8'), admin_user['password'].encode('utf-8')):
            print("✅ Admin login credentials verified!")
            print(f"   Email: {admin_user['email']}")
            print(f"   Role: {admin_user['role']}")
        else:
            print("❌ Password verification failed")
    else:
        print("❌ Admin user not found")
    
    # Test instructor login
    instructor_user = await users.find_one({"email": "<EMAIL>"})
    if instructor_user:
        print("✅ Instructor account available")
        print(f"   Email: {instructor_user['email']}")
        print(f"   Role: {instructor_user['role']}")
    
    # Test student login
    student_user = await users.find_one({"email": "<EMAIL>"})
    if student_user:
        print("✅ Student account available")
        print(f"   Email: {student_user['email']}")
        print(f"   Role: {student_user['role']}")

async def main():
    """Main test function"""
    print("🚀 Development Setup Test")
    print("=" * 60)
    
    # Test mock database
    await test_mock_database()
    
    # Create super admin
    await create_super_admin()
    
    # Test login credentials
    await test_login_credentials()
    
    print("\n" + "=" * 60)
    print("🎯 DEVELOPMENT CREDENTIALS")
    print("=" * 60)
    print("Super Admin:")
    print("  Email: <EMAIL>")
    print("  Password: Admin123!")
    print()
    print("Instructor (for testing):")
    print("  Email: <EMAIL>")
    print("  Password: Admin123!")
    print()
    print("Student (for testing):")
    print("  Email: <EMAIL>")
    print("  Password: Admin123!")
    print()
    print("🌐 Access the system at:")
    print("  Frontend: http://localhost:3000")
    print("  Backend API: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(main())
