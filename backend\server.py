from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timedelta
import bcrypt
import jwt
from enum import Enum

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# JWT Configuration
JWT_SECRET = "lms-secret-key-change-in-production"
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Security
security = HTTPBearer()

# Enums
class UserRole(str, Enum):
    STUDENT = "student"
    INSTRUCTOR = "instructor"
    ADMIN = "admin"

class LessonType(str, Enum):
    VIDEO = "video"
    TEXT = "text"
    QUIZ = "quiz"

# Models
class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    name: str
    role: UserRole
    created_at: datetime = Field(default_factory=datetime.utcnow)
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

class UserCreate(BaseModel):
    email: str
    password: str
    name: str
    role: UserRole = UserRole.STUDENT

class UserLogin(BaseModel):
    email: str
    password: str

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    role: Optional[UserRole] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None

class Course(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    instructor_id: str
    instructor_name: str
    cover_image: Optional[str] = None
    price: float = 0.0  # 0 for free courses
    is_published: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    category: str = "AI & Technology"
    duration_minutes: Optional[int] = None
    student_count: int = 0

class CourseCreate(BaseModel):
    title: str
    description: str
    cover_image: Optional[str] = None
    price: float = 0.0
    category: str = "AI & Technology"
    duration_minutes: Optional[int] = None

class Lesson(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    course_id: str
    title: str
    description: Optional[str] = None
    type: LessonType
    content: str  # For video: YouTube URL, for text: HTML content
    order: int
    duration_minutes: Optional[int] = None
    is_published: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)

class LessonCreate(BaseModel):
    title: str
    description: Optional[str] = None
    type: LessonType
    content: str
    order: int
    duration_minutes: Optional[int] = None

class Enrollment(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    course_id: str
    enrolled_at: datetime = Field(default_factory=datetime.utcnow)
    progress: Dict[str, Any] = {}  # lesson_id: completion_data
    completion_percentage: float = 0.0
    completed_at: Optional[datetime] = None

class Progress(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    course_id: str
    lesson_id: str
    completed: bool = False
    completed_at: Optional[datetime] = None
    time_spent_minutes: int = 0

class Discussion(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    course_id: str
    user_id: str
    user_name: str
    message: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_instructor: bool = False

class DiscussionCreate(BaseModel):
    message: str

class CourseReview(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    course_id: str
    user_id: str
    user_name: str
    rating: int  # 1-5 stars
    review: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

class ReviewCreate(BaseModel):
    rating: int
    review: str

class NewsletterSubscriber(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    name: Optional[str] = None
    subscribed_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True

class NewsletterSubscribe(BaseModel):
    email: str
    name: Optional[str] = None

class SiteContent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    section: str  # hero, features, testimonials, etc.
    content_type: str  # text, image, json
    content_key: str  # specific identifier
    content_value: str  # the actual content
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class SiteContentUpdate(BaseModel):
    content_value: str

class WebinarEvent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    date_time: datetime
    duration_minutes: int
    max_attendees: Optional[int] = None
    registration_link: Optional[str] = None
    instructor_id: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)

class WebinarCreate(BaseModel):
    title: str
    description: str
    date_time: datetime
    duration_minutes: int
    max_attendees: Optional[int] = None
    registration_link: Optional[str] = None

class BrandingSettings(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    logo_url: str
    site_title: str
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class BrandingUpdate(BaseModel):
    logo_url: Optional[str] = None
    site_title: Optional[str] = None

class InstructorPageContent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    hero_title: str
    hero_subtitle: str
    cta_title: str
    cta_subtitle: str
    cta_button_text: str
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class InstructorPageUpdate(BaseModel):
    hero_title: Optional[str] = None
    hero_subtitle: Optional[str] = None
    cta_title: Optional[str] = None
    cta_subtitle: Optional[str] = None
    cta_button_text: Optional[str] = None

class InstructorProfile(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    title: str
    avatar: str
    bio: str
    expertise: List[str]
    experience: str
    students: str
    rating: float
    courses: int
    company: str
    education: str
    order: int = 0
    is_active: bool = True
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class InstructorProfileCreate(BaseModel):
    name: str
    title: str
    avatar: str
    bio: str
    expertise: List[str]
    experience: str
    students: str
    rating: float = 4.9
    courses: int = 0
    company: str
    education: str
    order: int = 0

class InstructorProfileUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    avatar: Optional[str] = None
    bio: Optional[str] = None
    expertise: Optional[List[str]] = None
    experience: Optional[str] = None
    students: Optional[str] = None
    rating: Optional[float] = None
    courses: Optional[int] = None
    company: Optional[str] = None
    education: Optional[str] = None
    order: Optional[int] = None
    is_active: Optional[bool] = None

class Testimonial(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    title: str
    company: str
    avatar: Optional[str] = None
    rating: float = 5.0
    testimonial: str
    order: int = 0
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class TestimonialCreate(BaseModel):
    name: str
    title: str
    company: str
    avatar: Optional[str] = None
    rating: float = 5.0
    testimonial: str
    order: int = 0

class TestimonialUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    company: Optional[str] = None
    avatar: Optional[str] = None
    rating: Optional[float] = None
    testimonial: Optional[str] = None
    order: Optional[int] = None
    is_active: Optional[bool] = None

class ContactSubmission(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    email: str
    subject: str
    message: str
    phone: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_read: bool = False
    replied: bool = False

class ContactSubmissionCreate(BaseModel):
    name: str
    email: str
    subject: str
    message: str
    phone: Optional[str] = None

class ContactInfo(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    company_name: str
    address: str
    city: str
    state: str
    zip_code: str
    country: str
    phone: str
    email: str
    business_hours: str
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class ContactInfoUpdate(BaseModel):
    company_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    business_hours: Optional[str] = None

class SocialLinks(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    facebook: Optional[str] = None
    youtube: Optional[str] = None
    linkedin: Optional[str] = None
    twitter: Optional[str] = None  # X (formerly Twitter)
    instagram: Optional[str] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    updated_by: str

class SocialLinksUpdate(BaseModel):
    facebook: Optional[str] = None
    youtube: Optional[str] = None
    linkedin: Optional[str] = None
    twitter: Optional[str] = None
    instagram: Optional[str] = None

# Helper Functions
def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        token = credentials.credentials
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
        
        user = await db.users.find_one({"email": email})
        if user is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")
        
        return User(**user)
    except jwt.PyJWTError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    except Exception:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication failed")

# Auth Routes
@api_router.post("/auth/register")
async def register(user_data: UserCreate):
    # Check if user exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Hash password and create user
    hashed_password = hash_password(user_data.password)
    user_dict = user_data.dict()
    del user_dict['password']
    
    user = User(**user_dict)
    user_doc = user.dict()
    user_doc['password'] = hashed_password
    
    await db.users.insert_one(user_doc)
    
    # Create access token
    access_token = create_access_token(data={"sub": user.email})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user
    }

@api_router.post("/auth/login")
async def login(login_data: UserLogin):
    user = await db.users.find_one({"email": login_data.email})
    if not user or not verify_password(login_data.password, user['password']):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    access_token = create_access_token(data={"sub": user['email']})
    user_obj = User(**user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_obj
    }

@api_router.get("/auth/me")
async def get_me(current_user: User = Depends(get_current_user)):
    return current_user

@api_router.post("/auth/create-super-admin")
async def create_super_admin(admin_data: UserCreate):
    # Check if any admin user already exists
    existing_admin = await db.users.find_one({"role": UserRole.ADMIN})
    if existing_admin:
        raise HTTPException(status_code=400, detail="Super admin already exists")
    
    # Check if user with email already exists
    existing_user = await db.users.find_one({"email": admin_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Hash password
    hashed_password = hash_password(admin_data.password)
    
    # Create super admin user
    admin_user = User(
        email=admin_data.email,
        name=admin_data.name,
        role=UserRole.ADMIN
    )
    
    # Store user with hashed password
    user_dict = admin_user.dict()
    user_dict["password"] = hashed_password
    
    await db.users.insert_one(user_dict)
    
    # Return success message (don't return password)
    return {"message": "Super admin created successfully", "admin": {"name": admin_user.name, "email": admin_user.email, "role": admin_user.role}}

# Course Routes
@api_router.post("/courses", response_model=Course)
async def create_course(course_data: CourseCreate, current_user: User = Depends(get_current_user)):
    if current_user.role not in [UserRole.INSTRUCTOR, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="Only instructors can create courses")
    
    course_dict = course_data.dict()
    course_dict['instructor_id'] = current_user.id
    course_dict['instructor_name'] = current_user.name
    
    course = Course(**course_dict)
    await db.courses.insert_one(course.dict())
    
    return course

@api_router.get("/courses", response_model=List[Course])
async def get_courses(published_only: bool = True):
    query = {"is_published": True} if published_only else {}
    courses = await db.courses.find(query).to_list(1000)
    return [Course(**course) for course in courses]

@api_router.get("/courses/{course_id}", response_model=Course)
async def get_course(course_id: str):
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    return Course(**course)

@api_router.put("/courses/{course_id}", response_model=Course)
async def update_course(course_id: str, course_data: CourseCreate, current_user: User = Depends(get_current_user)):
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    if course['instructor_id'] != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not authorized to update this course")
    
    update_data = course_data.dict()
    update_data['updated_at'] = datetime.utcnow()
    
    await db.courses.update_one({"id": course_id}, {"$set": update_data})
    
    updated_course = await db.courses.find_one({"id": course_id})
    return Course(**updated_course)

@api_router.put("/courses/{course_id}/publish")
async def publish_course(course_id: str, current_user: User = Depends(get_current_user)):
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    if course['instructor_id'] != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not authorized to publish this course")
    
    await db.courses.update_one({"id": course_id}, {"$set": {"is_published": True}})
    return {"message": "Course published successfully"}

# Lesson Routes
@api_router.post("/courses/{course_id}/lessons", response_model=Lesson)
async def create_lesson(course_id: str, lesson_data: LessonCreate, current_user: User = Depends(get_current_user)):
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    if course['instructor_id'] != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not authorized to add lessons to this course")
    
    lesson_dict = lesson_data.dict()
    lesson_dict['course_id'] = course_id
    
    lesson = Lesson(**lesson_dict)
    await db.lessons.insert_one(lesson.dict())
    
    return lesson

@api_router.get("/courses/{course_id}/lessons", response_model=List[Lesson])
async def get_course_lessons(course_id: str):
    lessons = await db.lessons.find({"course_id": course_id}).sort("order", 1).to_list(1000)
    return [Lesson(**lesson) for lesson in lessons]

@api_router.get("/lessons/{lesson_id}", response_model=Lesson)
async def get_lesson(lesson_id: str):
    lesson = await db.lessons.find_one({"id": lesson_id})
    if not lesson:
        raise HTTPException(status_code=404, detail="Lesson not found")
    return Lesson(**lesson)

# Enrollment Routes
@api_router.post("/courses/{course_id}/enroll")
async def enroll_in_course(course_id: str, current_user: User = Depends(get_current_user)):
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    if not course.get('is_published', False):
        raise HTTPException(status_code=400, detail="Course is not published yet")
    
    # Check if already enrolled
    existing_enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": course_id})
    if existing_enrollment:
        return {"message": "Already enrolled in this course", "enrollment_id": existing_enrollment["id"]}
    
    enrollment = Enrollment(user_id=current_user.id, course_id=course_id)
    await db.enrollments.insert_one(enrollment.dict())
    
    # Update course student count
    await db.courses.update_one({"id": course_id}, {"$inc": {"student_count": 1}})
    
    return {"message": "Enrolled successfully", "enrollment_id": enrollment.id}

@api_router.get("/my-courses", response_model=List[Course])
async def get_my_courses(current_user: User = Depends(get_current_user)):
    if current_user.role == UserRole.INSTRUCTOR:
        # Return courses created by instructor
        courses = await db.courses.find({"instructor_id": current_user.id}).to_list(1000)
    else:
        # Return enrolled courses for students
        enrollments = await db.enrollments.find({"user_id": current_user.id}).to_list(1000)
        course_ids = [e['course_id'] for e in enrollments]
        courses = await db.courses.find({"id": {"$in": course_ids}}).to_list(1000)
    
    return [Course(**course) for course in courses]

@api_router.get("/courses/{course_id}/progress")
async def get_course_progress(course_id: str, current_user: User = Depends(get_current_user)):
    enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": course_id})
    if not enrollment:
        raise HTTPException(status_code=404, detail="Not enrolled in this course")
    
    lessons = await db.lessons.find({"course_id": course_id}).sort("order", 1).to_list(1000)
    progress_records = await db.progress.find({"user_id": current_user.id, "course_id": course_id}).to_list(1000)
    
    progress_dict = {p['lesson_id']: p for p in progress_records}
    
    lesson_progress = []
    for lesson in lessons:
        progress = progress_dict.get(lesson['id'], {})
        lesson_progress.append({
            "lesson": Lesson(**lesson),
            "completed": progress.get('completed', False),
            "time_spent_minutes": progress.get('time_spent_minutes', 0)
        })
    
    return {
        "course_id": course_id,
        "total_lessons": len(lessons),
        "completed_lessons": len([p for p in progress_records if p.get('completed')]),
        "completion_percentage": enrollment['completion_percentage'],
        "lessons": lesson_progress
    }

@api_router.post("/lessons/{lesson_id}/complete")
async def complete_lesson(lesson_id: str, current_user: User = Depends(get_current_user)):
    lesson = await db.lessons.find_one({"id": lesson_id})
    if not lesson:
        raise HTTPException(status_code=404, detail="Lesson not found")
    
    # Check enrollment
    enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": lesson['course_id']})
    if not enrollment:
        raise HTTPException(status_code=403, detail="Not enrolled in this course")
    
    # Update or create progress
    progress = await db.progress.find_one({"user_id": current_user.id, "lesson_id": lesson_id})
    if progress:
        await db.progress.update_one(
            {"id": progress['id']}, 
            {"$set": {"completed": True, "completed_at": datetime.utcnow()}}
        )
    else:
        new_progress = Progress(
            user_id=current_user.id,
            course_id=lesson['course_id'],
            lesson_id=lesson_id,
            completed=True,
            completed_at=datetime.utcnow()
        )
        await db.progress.insert_one(new_progress.dict())
    
    # Update enrollment completion percentage
    total_lessons = await db.lessons.count_documents({"course_id": lesson['course_id']})
    completed_lessons = await db.progress.count_documents({
        "user_id": current_user.id, 
        "course_id": lesson['course_id'], 
        "completed": True
    })
    
    completion_percentage = (completed_lessons / total_lessons) * 100 if total_lessons > 0 else 0
    
    await db.enrollments.update_one(
        {"user_id": current_user.id, "course_id": lesson['course_id']},
        {"$set": {"completion_percentage": completion_percentage}}
    )
    
    return {"message": "Lesson completed", "completion_percentage": completion_percentage}

# Dashboard Stats
@api_router.get("/dashboard/stats")
async def get_dashboard_stats(current_user: User = Depends(get_current_user)):
    if current_user.role == UserRole.INSTRUCTOR:
        # Instructor stats
        courses_cursor = db.courses.find({"instructor_id": current_user.id})
        instructor_courses = await courses_cursor.to_list(1000)
        course_ids = [c['id'] for c in instructor_courses]
        
        total_courses = len(instructor_courses)
        total_students = await db.enrollments.count_documents({
            "course_id": {"$in": course_ids}
        }) if course_ids else 0
        
        # Get total reviews for instructor courses
        total_reviews = await db.reviews.count_documents({
            "course_id": {"$in": course_ids}
        }) if course_ids else 0
        
        return {
            "total_courses": total_courses,
            "total_students": total_students,
            "total_reviews": total_reviews,
            "role": current_user.role
        }
    else:
        # Student stats
        total_enrolled = await db.enrollments.count_documents({"user_id": current_user.id})
        completed_courses = await db.enrollments.count_documents({
            "user_id": current_user.id, 
            "completion_percentage": 100
        })
        
        return {
            "total_enrolled": total_enrolled,
            "completed_courses": completed_courses,
            "role": current_user.role
        }

# Discussion Routes
@api_router.post("/courses/{course_id}/discussions", response_model=Discussion)
async def create_discussion(course_id: str, discussion_data: DiscussionCreate, current_user: User = Depends(get_current_user)):
    # Check if user is enrolled or is the instructor
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    is_instructor = course['instructor_id'] == current_user.id
    if not is_instructor:
        enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": course_id})
        if not enrollment:
            raise HTTPException(status_code=403, detail="Must be enrolled in course to participate in discussions")
    
    discussion = Discussion(
        course_id=course_id,
        user_id=current_user.id,
        user_name=current_user.name,
        message=discussion_data.message,
        is_instructor=is_instructor
    )
    
    await db.discussions.insert_one(discussion.dict())
    return discussion

@api_router.get("/courses/{course_id}/discussions", response_model=List[Discussion])
async def get_course_discussions(course_id: str, current_user: User = Depends(get_current_user)):
    # Check if user has access to course
    course = await db.courses.find_one({"id": course_id})
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    is_instructor = course['instructor_id'] == current_user.id
    if not is_instructor:
        enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": course_id})
        if not enrollment:
            raise HTTPException(status_code=403, detail="Must be enrolled in course to view discussions")
    
    discussions = await db.discussions.find({"course_id": course_id}).sort("created_at", -1).to_list(1000)
    return [Discussion(**discussion) for discussion in discussions]

# Review Routes
@api_router.post("/courses/{course_id}/reviews", response_model=CourseReview)
async def create_review(course_id: str, review_data: ReviewCreate, current_user: User = Depends(get_current_user)):
    # Check if user is enrolled
    enrollment = await db.enrollments.find_one({"user_id": current_user.id, "course_id": course_id})
    if not enrollment:
        raise HTTPException(status_code=403, detail="Must be enrolled in course to leave a review")
    
    # Check if user already reviewed
    existing_review = await db.reviews.find_one({"user_id": current_user.id, "course_id": course_id})
    if existing_review:
        raise HTTPException(status_code=400, detail="You have already reviewed this course")
    
    if review_data.rating < 1 or review_data.rating > 5:
        raise HTTPException(status_code=400, detail="Rating must be between 1 and 5")
    
    review = CourseReview(
        course_id=course_id,
        user_id=current_user.id,
        user_name=current_user.name,
        rating=review_data.rating,
        review=review_data.review
    )
    
    await db.reviews.insert_one(review.dict())
    return review

@api_router.get("/courses/{course_id}/reviews", response_model=List[CourseReview])
async def get_course_reviews(course_id: str):
    reviews = await db.reviews.find({"course_id": course_id}).sort("created_at", -1).to_list(1000)
    return [CourseReview(**review) for review in reviews]

# Newsletter Routes
@api_router.post("/newsletter/subscribe")
async def subscribe_newsletter(subscriber_data: NewsletterSubscribe):
    # Check if already subscribed
    existing = await db.newsletter_subscribers.find_one({"email": subscriber_data.email})
    if existing:
        if existing.get('is_active', True):
            return {"message": "Already subscribed to newsletter"}
        else:
            # Reactivate subscription
            await db.newsletter_subscribers.update_one(
                {"email": subscriber_data.email},
                {"$set": {"is_active": True, "subscribed_at": datetime.utcnow()}}
            )
            return {"message": "Newsletter subscription reactivated"}
    
    subscriber = NewsletterSubscriber(**subscriber_data.dict())
    await db.newsletter_subscribers.insert_one(subscriber.dict())
    return {"message": "Successfully subscribed to newsletter"}

@api_router.get("/newsletter/subscribers")
async def get_newsletter_subscribers(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Only instructors can view subscribers")
    
    subscribers = await db.newsletter_subscribers.find({"is_active": True}).to_list(1000)
    return {
        "count": len(subscribers),
        "subscribers": [NewsletterSubscriber(**sub) for sub in subscribers]
    }

@api_router.get("/newsletter/integration-guide")
async def get_newsletter_integration_guide():
    """Provides guidance for integrating newsletter with real email services"""
    return {
        "guide": {
            "title": "Newsletter Integration Guide",
            "description": "How to connect your newsletter to real email marketing services",
            "integrations": [
                {
                    "service": "SendGrid",
                    "description": "Reliable email delivery with great APIs",
                    "setup_steps": [
                        "1. Sign up at sendgrid.com and get API key",
                        "2. Add SENDGRID_API_KEY to your .env file",
                        "3. Install sendgrid library: pip install sendgrid",
                        "4. Update the newsletter endpoint to send welcome emails",
                        "5. Create email templates for drip campaigns"
                    ],
                    "cost": "Free tier: 100 emails/day, Paid: $15+/month"
                },
                {
                    "service": "Mailchimp",
                    "description": "Full-featured email marketing platform",
                    "setup_steps": [
                        "1. Create Mailchimp account and get API key",
                        "2. Add MAILCHIMP_API_KEY to your .env file", 
                        "3. Install mailchimp library: pip install mailchimp3",
                        "4. Create audience/list in Mailchimp dashboard",
                        "5. Update signup endpoint to add subscribers to list"
                    ],
                    "cost": "Free tier: 2,000 contacts, Paid: $10+/month"
                },
                {
                    "service": "ConvertKit",
                    "description": "Creator-focused email marketing (great for coaches)",
                    "setup_steps": [
                        "1. Sign up at convertkit.com and get API key",
                        "2. Add CONVERTKIT_API_KEY to your .env file",
                        "3. Install requests library for API calls",
                        "4. Create forms and sequences in ConvertKit",
                        "5. Set up automated drip campaigns"
                    ],
                    "cost": "Free tier: 1,000 subscribers, Paid: $29+/month"
                }
            ],
            "implementation_example": {
                "description": "Example integration with SendGrid",
                "code": """
# Add to requirements.txt
sendgrid==6.9.7

# Add to .env file
SENDGRID_API_KEY=your_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Update newsletter endpoint in server.py
import sendgrid
from sendgrid.helpers.mail import Mail

@api_router.post("/newsletter/subscribe")
async def subscribe_newsletter(subscriber_data: NewsletterSubscribe):
    # ... existing code ...
    
    # Send welcome email
    sg = sendgrid.SendGridAPIClient(api_key=os.environ.get('SENDGRID_API_KEY'))
    message = Mail(
        from_email=os.environ.get('SENDGRID_FROM_EMAIL'),
        to_emails=subscriber_data.email,
        subject='Welcome to AI Academy!',
        html_content='<h1>Welcome!</h1><p>Thanks for subscribing to AI updates...</p>'
    )
    
    try:
        response = sg.send(message)
        print(f"Email sent: {response.status_code}")
    except Exception as e:
        print(f"Email failed: {e}")
    
    return {"message": "Successfully subscribed to newsletter"}
                """
            },
            "marketing_automation": {
                "description": "Advanced marketing funnel setup",
                "strategies": [
                    "1. Welcome sequence: 3-email series introducing your expertise",
                    "2. Educational drip: Weekly AI insights and tips",
                    "3. Course announcements: New course launches and updates",
                    "4. Engagement campaigns: Ask questions, gather feedback",
                    "5. Upsell sequences: Guide free users to paid courses"
                ]
            }
        }
    }

# CMS Admin Routes
@api_router.get("/admin/analytics")
async def get_admin_analytics(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Get comprehensive analytics
    total_courses = await db.courses.count_documents({})
    total_students = await db.users.count_documents({"role": "student"})
    total_instructors = await db.users.count_documents({"role": "instructor"})
    total_enrollments = await db.enrollments.count_documents({})
    newsletter_subscribers = await db.newsletter_subscribers.count_documents({"is_active": True})
    
    # Get recent activity - limited and simplified
    recent_users_cursor = db.users.find({}).sort("created_at", -1).limit(5)
    recent_users_raw = await recent_users_cursor.to_list(5)
    
    recent_users = []
    for user in recent_users_raw:
        recent_users.append({
            "name": user["name"],
            "email": user["email"],
            "role": user["role"],
            "created_at": user["created_at"].isoformat() if hasattr(user["created_at"], 'isoformat') else str(user["created_at"])
        })
    
    return {
        "overview": {
            "total_courses": total_courses,
            "total_students": total_students,
            "total_instructors": total_instructors,
            "total_enrollments": total_enrollments,
            "newsletter_subscribers": newsletter_subscribers
        },
        "recent_activity": {
            "new_users": recent_users
        }
    }

@api_router.get("/admin/content")
async def get_site_content(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    content_items = await db.site_content.find({}).to_list(1000)
    return {"content": [SiteContent(**item) for item in content_items]}

@api_router.put("/admin/content/{content_key}")
async def update_site_content(content_key: str, content_update: SiteContentUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Update existing content or create new
    existing = await db.site_content.find_one({"content_key": content_key})
    if existing:
        await db.site_content.update_one(
            {"content_key": content_key},
            {"$set": {
                "content_value": content_update.content_value,
                "updated_at": datetime.utcnow(),
                "updated_by": current_user.id
            }}
        )
    else:
        new_content = SiteContent(
            section="general",
            content_type="text",
            content_key=content_key,
            content_value=content_update.content_value,
            updated_by=current_user.id
        )
        await db.site_content.insert_one(new_content.dict())
    
    return {"message": "Content updated successfully"}

@api_router.post("/admin/webinars", response_model=WebinarEvent)
async def create_webinar(webinar_data: WebinarCreate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    webinar = WebinarEvent(**webinar_data.dict(), instructor_id=current_user.id)
    await db.webinars.insert_one(webinar.dict())
    return webinar

@api_router.get("/admin/webinars", response_model=List[WebinarEvent])
async def get_admin_webinars(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    webinars = await db.webinars.find({}).sort("date_time", 1).to_list(1000)
    return [WebinarEvent(**webinar) for webinar in webinars]

@api_router.get("/webinars", response_model=List[WebinarEvent])
async def get_public_webinars():
    """Public endpoint for upcoming webinars"""
    webinars = await db.webinars.find({
        "is_active": True,
        "date_time": {"$gte": datetime.utcnow()}
    }).sort("date_time", 1).to_list(1000)
    return [WebinarEvent(**webinar) for webinar in webinars]

# User Management Routes (Admin Only)
@api_router.get("/admin/users")
async def get_all_users(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Super admin access required")
    
    users = await db.users.find({}).to_list(1000)
    return {
        "users": [{
            "id": u["id"],
            "name": u["name"],
            "email": u["email"],
            "role": u["role"],
            "created_at": u["created_at"],
            "bio": u.get("bio"),
            "avatar_url": u.get("avatar_url")
        } for u in users]
    }

@api_router.post("/admin/users", response_model=User)
async def create_user_by_admin(user_data: UserCreate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Super admin access required")
    
    # Check if user with email already exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Hash password
    hashed_password = bcrypt.hashpw(user_data.password.encode('utf-8'), bcrypt.gensalt())
    
    # Create user
    new_user = User(
        email=user_data.email,
        name=user_data.name,
        role=user_data.role
    )
    
    # Store user with hashed password
    user_dict = new_user.dict()
    user_dict["password"] = hashed_password.decode('utf-8')
    
    await db.users.insert_one(user_dict)
    return new_user

@api_router.put("/admin/users/{user_id}", response_model=User)
async def update_user_by_admin(user_id: str, user_data: UserUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Super admin access required")
    
    # Find the user
    user = await db.users.find_one({"id": user_id})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Prepare update data
    update_data = {}
    if user_data.name is not None:
        update_data["name"] = user_data.name
    if user_data.email is not None:
        # Check if email is unique
        existing_user = await db.users.find_one({"email": user_data.email, "id": {"$ne": user_id}})
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already in use")
        update_data["email"] = user_data.email
    if user_data.role is not None:
        update_data["role"] = user_data.role
    if user_data.bio is not None:
        update_data["bio"] = user_data.bio
    if user_data.avatar_url is not None:
        update_data["avatar_url"] = user_data.avatar_url
    
    # Update user
    await db.users.update_one({"id": user_id}, {"$set": update_data})
    
    # Return updated user
    updated_user = await db.users.find_one({"id": user_id})
    return User(**updated_user)

@api_router.delete("/admin/users/{user_id}")
async def delete_user_by_admin(user_id: str, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Super admin access required")
    
    # Check if user exists
    user = await db.users.find_one({"id": user_id})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Prevent admin from deleting themselves
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")
    
    # Delete user
    result = await db.users.delete_one({"id": user_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User deleted successfully"}

# Branding Management Routes
@api_router.get("/admin/branding")
async def get_branding_settings(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    branding = await db.branding_settings.find_one({})
    if not branding:
        # Return default branding with the new logo
        return {
            "logo_url": "https://static.wixstatic.com/media/e226d5_1613e089c216411ca2e365caaf93709f~mv2.jpg/v1/fill/w_200,h_170,al_c,q_80,usm_0.66_1.00_0.01,enc_avif,quality_auto/Logo%20500x500%20px_edited.jpg",
            "site_title": "AI Academy"
        }
    
    return BrandingSettings(**branding)

@api_router.put("/admin/branding")
async def update_branding_settings(branding_data: BrandingUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.branding_settings.find_one({})
    
    update_data = {}
    if branding_data.logo_url is not None:
        update_data["logo_url"] = branding_data.logo_url
    if branding_data.site_title is not None:
        update_data["site_title"] = branding_data.site_title
    
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    if existing:
        await db.branding_settings.update_one({}, {"$set": update_data})
    else:
        new_branding = BrandingSettings(
            logo_url=branding_data.logo_url or "https://i.imgur.com/VQZvQxS.png",
            site_title=branding_data.site_title or "AI Academy",
            updated_by=current_user.id
        )
        await db.branding_settings.insert_one(new_branding.dict())
    
    return {"message": "Branding updated successfully"}

@api_router.get("/branding")
async def get_public_branding():
    """Public endpoint for getting current branding"""
    branding = await db.branding_settings.find_one({})
    if not branding:
        return {
            "logo_url": "https://static.wixstatic.com/media/e226d5_1613e089c216411ca2e365caaf93709f~mv2.jpg/v1/fill/w_200,h_170,al_c,q_80,usm_0.66_1.00_0.01,enc_avif,quality_auto/Logo%20500x500%20px_edited.jpg",
            "site_title": "AI Academy"
        }
    
    return {
        "logo_url": branding["logo_url"],
        "site_title": branding["site_title"]
    }

# Instructor Page Management Routes
@api_router.get("/admin/instructor-page")
async def get_instructor_page_content(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    content = await db.instructor_page_content.find_one({})
    if not content:
        # Return default content
        return {
            "hero_title": "Meet Our Expert Instructors",
            "hero_subtitle": "Learn from industry leaders and AI pioneers who are shaping the future",
            "cta_title": "Ready to Learn from the Best?",
            "cta_subtitle": "Join thousands of students who are advancing their careers with expert-led AI courses",
            "cta_button_text": "Get Started"
        }
    
    return InstructorPageContent(**content)

@api_router.put("/admin/instructor-page")
async def update_instructor_page_content(page_data: InstructorPageUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.instructor_page_content.find_one({})
    
    update_data = {}
    if page_data.hero_title is not None:
        update_data["hero_title"] = page_data.hero_title
    if page_data.hero_subtitle is not None:
        update_data["hero_subtitle"] = page_data.hero_subtitle
    if page_data.cta_title is not None:
        update_data["cta_title"] = page_data.cta_title
    if page_data.cta_subtitle is not None:
        update_data["cta_subtitle"] = page_data.cta_subtitle
    if page_data.cta_button_text is not None:
        update_data["cta_button_text"] = page_data.cta_button_text
    
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    if existing:
        await db.instructor_page_content.update_one({}, {"$set": update_data})
    else:
        new_content = InstructorPageContent(
            hero_title=page_data.hero_title or "Meet Our Expert Instructors",
            hero_subtitle=page_data.hero_subtitle or "Learn from industry leaders and AI pioneers who are shaping the future",
            cta_title=page_data.cta_title or "Ready to Learn from the Best?",
            cta_subtitle=page_data.cta_subtitle or "Join thousands of students who are advancing their careers with expert-led AI courses",
            cta_button_text=page_data.cta_button_text or "Get Started",
            updated_by=current_user.id
        )
        await db.instructor_page_content.insert_one(new_content.dict())
    
    return {"message": "Instructor page content updated successfully"}

@api_router.get("/instructor-page-content")
async def get_public_instructor_page_content():
    """Public endpoint for getting instructor page content"""
    content = await db.instructor_page_content.find_one({})
    if not content:
        return {
            "hero_title": "Meet Our Expert Instructors",
            "hero_subtitle": "Learn from industry leaders and AI pioneers who are shaping the future",
            "cta_title": "Ready to Learn from the Best?",
            "cta_subtitle": "Join thousands of students who are advancing their careers with expert-led AI courses",
            "cta_button_text": "Get Started"
        }
    
    return {
        "hero_title": content["hero_title"],
        "hero_subtitle": content["hero_subtitle"],
        "cta_title": content["cta_title"],
        "cta_subtitle": content["cta_subtitle"],
        "cta_button_text": content["cta_button_text"]
    }

# Instructor Profile Management Routes
@api_router.get("/admin/instructor-profiles", response_model=List[InstructorProfile])
async def get_instructor_profiles(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    profiles = await db.instructor_profiles.find({}).sort("order", 1).to_list(1000)
    return [InstructorProfile(**profile) for profile in profiles]

@api_router.post("/admin/instructor-profiles", response_model=InstructorProfile)
async def create_instructor_profile(profile_data: InstructorProfileCreate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    profile = InstructorProfile(**profile_data.dict(), updated_by=current_user.id)
    await db.instructor_profiles.insert_one(profile.dict())
    return profile

@api_router.put("/admin/instructor-profiles/{profile_id}", response_model=InstructorProfile)
async def update_instructor_profile(profile_id: str, profile_data: InstructorProfileUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.instructor_profiles.find_one({"id": profile_id})
    if not existing:
        raise HTTPException(status_code=404, detail="Instructor profile not found")
    
    update_data = {k: v for k, v in profile_data.dict().items() if v is not None}
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    await db.instructor_profiles.update_one({"id": profile_id}, {"$set": update_data})
    
    updated_profile = await db.instructor_profiles.find_one({"id": profile_id})
    return InstructorProfile(**updated_profile)

@api_router.delete("/admin/instructor-profiles/{profile_id}")
async def delete_instructor_profile(profile_id: str, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    result = await db.instructor_profiles.delete_one({"id": profile_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Instructor profile not found")
    
    return {"message": "Instructor profile deleted successfully"}

@api_router.get("/instructor-profiles", response_model=List[InstructorProfile])
async def get_public_instructor_profiles():
    """Public endpoint for getting instructor profiles"""
    profiles = await db.instructor_profiles.find({"is_active": True}).sort("order", 1).to_list(1000)
    return [InstructorProfile(**profile) for profile in profiles]

@api_router.post("/admin/instructor-profiles/init-default")
async def init_default_instructors(current_user: User = Depends(get_current_user)):
    """Initialize default instructor profiles if none exist"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing_count = await db.instructor_profiles.count_documents({})
    if existing_count > 0:
        return {"message": "Instructor profiles already exist"}
    
    default_instructors = [
        InstructorProfile(
            name="AI Coach",
            title="Senior AI Researcher & Educator",
            avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
            bio="Former Google AI researcher with 10+ years experience in machine learning and deep learning. Published 50+ papers and taught over 10,000 students worldwide.",
            expertise=["Machine Learning", "Deep Learning", "Computer Vision", "NLP"],
            experience="10+ years",
            students="10,000+",
            rating=4.9,
            courses=12,
            company="Former Google AI",
            education="PhD Computer Science - Stanford",
            order=1,
            updated_by=current_user.id
        ),
        InstructorProfile(
            name="Dr. Sarah Kim",
            title="Data Science Expert & Consultant",
            avatar="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
            bio="Leading data scientist with experience at Netflix and Uber. Specializes in recommendation systems and real-world AI applications.",
            expertise=["Data Science", "Recommendation Systems", "Python", "Statistics"],
            experience="8+ years",
            students="5,000+",
            rating=4.8,
            courses=8,
            company="Former Netflix",
            education="MS Data Science - MIT",
            order=2,
            updated_by=current_user.id
        ),
        InstructorProfile(
            name="Marcus Johnson",
            title="AI Ethics & Strategy Advisor",
            avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            bio="AI strategy consultant for Fortune 500 companies. Expert in responsible AI development and business implementation.",
            expertise=["AI Strategy", "Ethics", "Business Intelligence", "Leadership"],
            experience="12+ years",
            students="3,000+",
            rating=4.9,
            courses=6,
            company="McKinsey & Company",
            education="MBA - Harvard Business School",
            order=3,
            updated_by=current_user.id
        )
    ]
    
    for instructor in default_instructors:
        await db.instructor_profiles.insert_one(instructor.dict())
    
    return {"message": "Default instructor profiles created successfully"}

# Testimonials Management Routes
@api_router.get("/admin/testimonials", response_model=List[Testimonial])
async def get_testimonials(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    testimonials = await db.testimonials.find({}).sort("order", 1).to_list(1000)
    return [Testimonial(**testimonial) for testimonial in testimonials]

@api_router.post("/admin/testimonials", response_model=Testimonial)
async def create_testimonial(testimonial_data: TestimonialCreate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    testimonial = Testimonial(**testimonial_data.dict(), updated_by=current_user.id)
    await db.testimonials.insert_one(testimonial.dict())
    return testimonial

@api_router.put("/admin/testimonials/{testimonial_id}", response_model=Testimonial)
async def update_testimonial(testimonial_id: str, testimonial_data: TestimonialUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.testimonials.find_one({"id": testimonial_id})
    if not existing:
        raise HTTPException(status_code=404, detail="Testimonial not found")
    
    update_data = {k: v for k, v in testimonial_data.dict().items() if v is not None}
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    await db.testimonials.update_one({"id": testimonial_id}, {"$set": update_data})
    
    updated_testimonial = await db.testimonials.find_one({"id": testimonial_id})
    return Testimonial(**updated_testimonial)

@api_router.delete("/admin/testimonials/{testimonial_id}")
async def delete_testimonial(testimonial_id: str, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    result = await db.testimonials.delete_one({"id": testimonial_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Testimonial not found")
    
    return {"message": "Testimonial deleted successfully"}

@api_router.get("/testimonials", response_model=List[Testimonial])
async def get_public_testimonials():
    """Public endpoint for getting testimonials"""
    testimonials = await db.testimonials.find({"is_active": True}).sort("order", 1).to_list(1000)
    return [Testimonial(**testimonial) for testimonial in testimonials]

@api_router.post("/admin/testimonials/init-default")
async def init_default_testimonials(current_user: User = Depends(get_current_user)):
    """Initialize default testimonials if none exist"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing_count = await db.testimonials.count_documents({})
    if existing_count > 0:
        return {"message": "Testimonials already exist"}
    
    default_testimonials = [
        Testimonial(
            name="Sarah Rodriguez",
            title="Data Scientist",
            company="Google",
            avatar="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
            rating=5.0,
            testimonial="This course completely transformed my understanding of machine learning. The hands-on projects were incredibly valuable for my career transition.",
            order=1,
            updated_by=current_user.id
        ),
        Testimonial(
            name="Michael Chen",
            title="AI Engineer",
            company="Tesla",
            avatar="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
            rating=5.0,
            testimonial="The instructor's expertise and clear explanations made complex AI concepts easy to understand. Highly recommended for anyone serious about AI.",
            order=2,
            updated_by=current_user.id
        ),
        Testimonial(
            name="Emily Parker",
            title="Product Manager",
            company="Microsoft",
            avatar="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
            rating=5.0,
            testimonial="Perfect blend of theory and practice. I was able to implement AI solutions at work within weeks of completing the course.",
            order=3,
            updated_by=current_user.id
        )
    ]
    
    for testimonial in default_testimonials:
        await db.testimonials.insert_one(testimonial.dict())
    
    return {"message": "Default testimonials created successfully"}

# Contact Form & Info Management Routes
@api_router.post("/contact/submit", response_model=ContactSubmission)
async def submit_contact_form(contact_data: ContactSubmissionCreate):
    """Public endpoint for contact form submissions"""
    contact = ContactSubmission(**contact_data.dict())
    await db.contact_submissions.insert_one(contact.dict())
    return contact

@api_router.get("/admin/contact-submissions", response_model=List[ContactSubmission])
async def get_contact_submissions(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    submissions = await db.contact_submissions.find({}).sort("created_at", -1).to_list(1000)
    return [ContactSubmission(**submission) for submission in submissions]

@api_router.put("/admin/contact-submissions/{submission_id}/mark-read")
async def mark_contact_read(submission_id: str, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    await db.contact_submissions.update_one(
        {"id": submission_id}, 
        {"$set": {"is_read": True}}
    )
    return {"message": "Contact marked as read"}

@api_router.get("/admin/contact-info")
async def get_contact_info(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    contact_info = await db.contact_info.find_one({})
    if not contact_info:
        return {
            "company_name": "AI Academy",
            "address": "123 Tech Street",
            "city": "San Francisco",
            "state": "CA",
            "zip_code": "94105",
            "country": "USA",
            "phone": "+****************",
            "email": "<EMAIL>",
            "business_hours": "Monday - Friday: 9:00 AM - 6:00 PM PST"
        }
    
    return ContactInfo(**contact_info)

@api_router.put("/admin/contact-info")
async def update_contact_info(contact_data: ContactInfoUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.contact_info.find_one({})
    
    update_data = {k: v for k, v in contact_data.dict().items() if v is not None}
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    if existing:
        await db.contact_info.update_one({}, {"$set": update_data})
    else:
        new_contact_info = ContactInfo(
            company_name=contact_data.company_name or "AI Academy",
            address=contact_data.address or "123 Tech Street",
            city=contact_data.city or "San Francisco", 
            state=contact_data.state or "CA",
            zip_code=contact_data.zip_code or "94105",
            country=contact_data.country or "USA",
            phone=contact_data.phone or "+****************",
            email=contact_data.email or "<EMAIL>",
            business_hours=contact_data.business_hours or "Monday - Friday: 9:00 AM - 6:00 PM PST",
            updated_by=current_user.id
        )
        await db.contact_info.insert_one(new_contact_info.dict())
    
    return {"message": "Contact info updated successfully"}

@api_router.get("/contact-info")
async def get_public_contact_info():
    """Public endpoint for contact information"""
    contact_info = await db.contact_info.find_one({})
    if not contact_info:
        return {
            "company_name": "AI Academy",
            "address": "123 Tech Street",
            "city": "San Francisco",
            "state": "CA", 
            "zip_code": "94105",
            "country": "USA",
            "phone": "+****************",
            "email": "<EMAIL>",
            "business_hours": "Monday - Friday: 9:00 AM - 6:00 PM PST"
        }
    
    return {
        "company_name": contact_info["company_name"],
        "address": contact_info["address"],
        "city": contact_info["city"],
        "state": contact_info["state"],
        "zip_code": contact_info["zip_code"],
        "country": contact_info["country"],
        "phone": contact_info["phone"],
        "email": contact_info["email"],
        "business_hours": contact_info["business_hours"]
    }

# Social Links Management Routes
@api_router.get("/admin/social-links")
async def get_social_links(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    social_links = await db.social_links.find_one({})
    if not social_links:
        return {
            "facebook": "",
            "youtube": "",
            "linkedin": "",
            "twitter": "",
            "instagram": ""
        }
    
    return SocialLinks(**social_links)

@api_router.put("/admin/social-links")
async def update_social_links(social_data: SocialLinksUpdate, current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    existing = await db.social_links.find_one({})
    
    update_data = {k: v for k, v in social_data.dict().items() if v is not None}
    update_data["updated_at"] = datetime.utcnow()
    update_data["updated_by"] = current_user.id
    
    if existing:
        await db.social_links.update_one({}, {"$set": update_data})
    else:
        new_social_links = SocialLinks(
            facebook=social_data.facebook or "",
            youtube=social_data.youtube or "",
            linkedin=social_data.linkedin or "",
            twitter=social_data.twitter or "",
            instagram=social_data.instagram or "",
            updated_by=current_user.id
        )
        await db.social_links.insert_one(new_social_links.dict())
    
    return {"message": "Social links updated successfully"}

@api_router.get("/social-links")
async def get_public_social_links():
    """Public endpoint for social media links"""
    social_links = await db.social_links.find_one({})
    if not social_links:
        return {
            "facebook": "",
            "youtube": "",
            "linkedin": "",
            "twitter": "",
            "instagram": ""
        }
    
    return {
        "facebook": social_links.get("facebook", ""),
        "youtube": social_links.get("youtube", ""),
        "linkedin": social_links.get("linkedin", ""),
        "twitter": social_links.get("twitter", ""),
        "instagram": social_links.get("instagram", "")
    }

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()
